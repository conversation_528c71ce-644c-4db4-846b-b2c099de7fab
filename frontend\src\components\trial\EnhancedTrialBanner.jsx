/**
 * Enhanced Trial Banner - Enterprise-grade trial status notification and conversion component
 * Features: Comprehensive trial banner with advanced trial status tracking, countdown timers,
 * and upgrade prompts, detailed trial customization with dynamic messaging and personalized
 * upgrade paths, advanced trial features with usage analytics and conversion tracking, ACE Social's
 * subscription system integration with seamless trial-to-paid conversion workflows, trial interaction
 * features including dismissible banners and persistent reminders, trial state management with
 * real-time updates and trial extension handling, real-time trial updates with live countdown
 * displays and dynamic messaging, and seamless ACE Social platform integration with advanced
 * trial orchestration and comprehensive accessibility compliance
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 */

import {
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useCallback,
  useMemo,

} from 'react';
import PropTypes from 'prop-types';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Typography,
  Paper,
  Chip,
  LinearProgress,
  Link,
  Stack,

  IconButton,

  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Close as CloseIcon,
  Upgrade as UpgradeIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Support as SupportIcon
} from '@mui/icons-material';
import { useTrial } from '../../contexts/TrialContext';
import { useConfirmation } from '../../contexts/ConfirmationContext';
import { useNotification } from '../../contexts/NotificationContext';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Trial banner variants
const BANNER_VARIANTS = {
  FULL: 'full',
  COMPACT: 'compact',
  MINIMAL: 'minimal',
  FLOATING: 'floating'
};

// Trial urgency levels
const URGENCY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

// Trial positions
const BANNER_POSITIONS = {
  TOP: 'top',
  BOTTOM: 'bottom',
  FLOATING: 'floating',
  STICKY: 'sticky'
};

/**
 * Enhanced Trial Banner - Comprehensive trial status notification with advanced features
 * Implements detailed trial management and enterprise-grade conversion capabilities
 */
const EnhancedTrialBanner = memo(forwardRef(({
  variant = BANNER_VARIANTS.FULL,
  showFeatures = true,
  showProgress = true,
  autoHide = false,
  position = BANNER_POSITIONS.TOP,
  enableAccessibility = true,
  enableConversionTracking = true,
  showCountdown = true,
  onTrialAction,
  onAnalyticsTrack,
  onConversionEvent
}, ref) => {
  const navigate = useNavigate();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();


  const { 
    isTrial, 
    daysRemaining, 
    hoursRemaining, 
    trialEnd, 
    planId,
    isLoading,
    convertToPaid,
    cancelTrial
  } = useTrial();
  
  const { showConfirmation } = useConfirmation();
  const { showSuccessNotification, showErrorNotification } = useNotification();
  
  // Enhanced state management
  const [isMinimized, setIsMinimized] = useState(false);
  const [showFeatureDialog, setShowFeatureDialog] = useState(false);
  const [isConverting, setIsConverting] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);
  const [showExtensionDialog, setShowExtensionDialog] = useState(false);
  const [trialAnalytics, setTrialAnalytics] = useState({
    viewTime: 0,
    interactions: 0,
    conversionAttempts: 0,
    lastActivity: new Date().toISOString()
  });


  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    minimize: () => setIsMinimized(true),
    expand: () => setIsMinimized(false),
    dismiss: () => setIsDismissed(true),
    triggerUpgrade: () => handleUpgrade(),
    getAnalytics: () => trialAnalytics,
    resetAnalytics: () => setTrialAnalytics({
      viewTime: 0,
      interactions: 0,
      conversionAttempts: 0,
      lastActivity: new Date().toISOString()
    })
  }), [trialAnalytics, handleUpgrade]);



  // Enhanced utility functions
  const handleUpgrade = useCallback(async () => {
    setIsConverting(true);
    setTrialAnalytics(prev => ({
      ...prev,
      conversionAttempts: prev.conversionAttempts + 1,
      interactions: prev.interactions + 1,
      lastActivity: new Date().toISOString()
    }));

    if (enableAccessibility) {
      announceToScreenReader('Starting upgrade process');
    }

    if (onTrialAction) {
      onTrialAction('upgrade_initiated', {
        urgencyLevel,
        daysRemaining,
        timestamp: new Date().toISOString()
      });
    }

    try {
      const success = await convertToPaid();
      if (success) {
        showSuccessNotification('Successfully upgraded to premium!');

        if (onConversionEvent) {
          onConversionEvent('conversion_success', {
            fromTrial: true,
            planId,
            timestamp: new Date().toISOString()
          });
        }

        if (enableAccessibility) {
          announceToScreenReader('Upgrade successful! Welcome to premium!');
        }

        navigate('/billing/success');
      } else {
        navigate('/billing/plans');
      }
    } catch (error) {
      showErrorNotification('Failed to upgrade. Please try again.');

      if (onTrialAction) {
        onTrialAction('upgrade_failed', {
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }

      navigate('/billing/plans');
    } finally {
      setIsConverting(false);
    }
  }, [
    convertToPaid,
    showSuccessNotification,
    showErrorNotification,
    navigate,
    planId,
    urgencyLevel,
    daysRemaining,
    enableAccessibility,
    announceToScreenReader,
    onTrialAction,
    onConversionEvent
  ]);

  // Enhanced effects for component lifecycle
  useEffect(() => {
    if (enableAccessibility) {
      announceToScreenReader(`Trial banner displayed. ${daysRemaining} days remaining in trial.`);
    }

    if (enableConversionTracking && onAnalyticsTrack) {
      onAnalyticsTrack('trial_banner_viewed', {
        daysRemaining,
        urgencyLevel,
        variant,
        timestamp: new Date().toISOString()
      });
    }
  }, [
    enableAccessibility,
    announceToScreenReader,
    enableConversionTracking,
    onAnalyticsTrack,
    daysRemaining,
    urgencyLevel,
    variant
  ]);

  // Auto-hide logic with enhanced tracking
  useEffect(() => {
    if (autoHide && daysRemaining > 7) {
      const timer = setTimeout(() => {
        setIsMinimized(true);

        if (enableAccessibility) {
          announceToScreenReader('Trial banner minimized automatically');
        }
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [autoHide, daysRemaining, enableAccessibility, announceToScreenReader]);

  // Enhanced countdown timer
  useEffect(() => {
    if (!showCountdown) return;

    const updateCountdown = () => {
      const now = new Date();
      const end = new Date(trialEnd);
      const diff = end - now;

      if (diff > 0) {
        // Countdown logic can be implemented here if needed
        // For now, we use the existing daysRemaining and hoursRemaining
      }
    };

    const interval = setInterval(updateCountdown, 1000);
    updateCountdown(); // Initial call

    return () => clearInterval(interval);
  }, [showCountdown, trialEnd]);

  // Analytics tracking for view time
  useEffect(() => {
    const startTime = Date.now();

    return () => {
      const viewTime = Date.now() - startTime;
      setTrialAnalytics(prev => ({
        ...prev,
        viewTime: prev.viewTime + viewTime
      }));
    };
  }, []);

  // Enhanced interaction handlers (moved before early return)
  const handleDismiss = useCallback(() => {
    setIsDismissed(true);

    if (enableAccessibility) {
      announceToScreenReader('Trial banner dismissed');
    }

    if (onTrialAction) {
      onTrialAction('banner_dismissed', {
        daysRemaining,
        urgencyLevel: daysRemaining <= 1 ? 'critical' : daysRemaining <= 3 ? 'high' : 'medium',
        timestamp: new Date().toISOString()
      });
    }
  }, [enableAccessibility, announceToScreenReader, onTrialAction, daysRemaining]);

  const handleMinimize = useCallback(() => {
    setIsMinimized(true);

    if (enableAccessibility) {
      announceToScreenReader('Trial banner minimized');
    }

    if (onTrialAction) {
      onTrialAction('banner_minimized', {
        daysRemaining,
        timestamp: new Date().toISOString()
      });
    }
  }, [enableAccessibility, announceToScreenReader, onTrialAction, daysRemaining]);

  const handleCancel = useCallback(() => {
    if (onTrialAction) {
      onTrialAction('cancel_initiated', {
        daysRemaining,
        timestamp: new Date().toISOString()
      });
    }

    showConfirmation({
      title: 'Cancel Trial?',
      content: 'Are you sure you want to cancel your trial? You\'ll lose access to premium features immediately and be downgraded to the Creator plan.',
      confirmText: 'Cancel Trial',
      cancelText: 'Keep Trial',
      confirmColor: 'error',
      onConfirm: async () => {
        const success = await cancelTrial();
        if (success) {
          showSuccessNotification('Trial cancelled successfully');

          if (enableAccessibility) {
            announceToScreenReader('Trial cancelled successfully');
          }

          if (onTrialAction) {
            onTrialAction('trial_cancelled', {
              timestamp: new Date().toISOString()
            });
          }
        }
      }
    });
  }, [
    showConfirmation,
    cancelTrial,
    showSuccessNotification,
    enableAccessibility,
    announceToScreenReader,
    onTrialAction,
    daysRemaining
  ]);

  const handleExtensionRequest = useCallback(() => {
    if (onTrialAction) {
      onTrialAction('extension_requested', {
        daysRemaining,
        timestamp: new Date().toISOString()
      });
    }

    setShowExtensionDialog(true);
  }, [onTrialAction, daysRemaining]);

  const handleExtensionConfirm = useCallback(() => {
    setShowExtensionDialog(false);

    if (enableAccessibility) {
      announceToScreenReader('Opening support for trial extension');
    }

    window.open('/support?topic=trial-extension', '_blank');

    if (onTrialAction) {
      onTrialAction('extension_support_opened', {
        timestamp: new Date().toISOString()
      });
    }
  }, [enableAccessibility, announceToScreenReader, onTrialAction]);

  // Enhanced urgency calculation with memoization
  const urgencyData = useMemo(() => {
    let level = URGENCY_LEVELS.LOW;
    let color = 'info';
    let icon = <ScheduleIcon />;
    let bgColor = ACE_COLORS.PURPLE;

    if (daysRemaining <= 1) {
      level = URGENCY_LEVELS.CRITICAL;
      color = 'error';
      icon = <ErrorIcon />;
      bgColor = '#F44336';
    } else if (daysRemaining <= 3) {
      level = URGENCY_LEVELS.HIGH;
      color = 'warning';
      icon = <WarningIcon />;
      bgColor = ACE_COLORS.YELLOW;
    } else if (daysRemaining <= 7) {
      level = URGENCY_LEVELS.MEDIUM;
      color = 'warning';
      icon = <ScheduleIcon />;
      bgColor = '#FF9800';
    }

    return { level, color, icon, bgColor };
  }, [daysRemaining]);

  const { level: urgencyLevel, color: urgencyColor, icon: urgencyIcon } = urgencyData;

  // Calculate progress (14 days trial)
  const totalDays = 14;
  const daysUsed = totalDays - daysRemaining;
  const progress = Math.min(100, (daysUsed / totalDays) * 100);

  // Format trial end date
  const formattedEndDate = trialEnd ? new Date(trialEnd).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }) : '';

  // Don't show if not on trial, still loading, or dismissed
  if (!isTrial || isLoading || isDismissed) return null;
  
  // Dynamic messaging based on urgency
  const getUrgencyMessage = () => {
    if (daysRemaining <= 1) {
      return `Your trial expires ${hoursRemaining <= 12 ? 'in just a few hours' : 'tomorrow'}! Don't lose access to your premium features.`;
    } else if (daysRemaining <= 3) {
      return `Only ${daysRemaining} days left in your trial. Upgrade now to keep all your premium features.`;
    } else if (daysRemaining <= 7) {
      return `Your trial ends on ${formattedEndDate}. Upgrade to continue enjoying premium features.`;
    } else {
      return `You're enjoying your premium trial! ${daysRemaining} days remaining to explore all features.`;
    }
  };
  

  
  // Compact variant
  if (variant === 'compact' || isMinimized) {
    return (
      <Paper
        elevation={2}
        sx={{
          p: 1.5,
          mb: 2,
          backgroundColor: (theme) => 
            urgencyLevel === 'critical' ? theme.palette.error.light :
            urgencyLevel === 'high' ? theme.palette.warning.light :
            theme.palette.info.light,
          border: (theme) => `1px solid ${
            urgencyLevel === 'critical' ? theme.palette.error.main :
            urgencyLevel === 'high' ? theme.palette.warning.main :
            theme.palette.info.main
          }`,
          borderRadius: 2,
          position: position === 'floating' ? 'fixed' : 'relative',
          top: position === 'floating' ? 20 : 'auto',
          right: position === 'floating' ? 20 : 'auto',
          zIndex: position === 'floating' ? 1300 : 'auto',
          maxWidth: position === 'floating' ? 400 : '100%'
        }}
      >
        <Stack direction="row" spacing={2} alignItems="center" justifyContent="space-between">
          <Stack direction="row" spacing={1} alignItems="center">
            {urgencyIcon}
            <Typography variant="body2" fontWeight="bold">
              {daysRemaining}d {hoursRemaining}h left
            </Typography>
          </Stack>
          
          <Stack direction="row" spacing={1}>
            <Button
              variant="contained"
              size="small"
              color={urgencyColor}
              onClick={handleUpgrade}
              disabled={isConverting}
              startIcon={<UpgradeIcon />}
            >
              Upgrade
            </Button>
            
            {!isMinimized && (
              <IconButton
                size="small"
                onClick={handleMinimize}
              >
                <CloseIcon fontSize="small" />
              </IconButton>
            )}
          </Stack>
        </Stack>
        
        {!isMinimized && showProgress && (
          <Box sx={{ mt: 1 }}>
            <LinearProgress 
              variant="determinate" 
              value={progress} 
              color={urgencyColor}
              sx={{ height: 6, borderRadius: 1 }}
            />
          </Box>
        )}
      </Paper>
    );
  }
  
  // Full variant
  return (
    <>
      <Paper
        elevation={3}
        sx={{
          p: 3,
          mb: 3,
          backgroundColor: (theme) => 
            urgencyLevel === 'critical' ? theme.palette.error.light :
            urgencyLevel === 'high' ? theme.palette.warning.light :
            theme.palette.info.light,
          border: (theme) => `2px solid ${
            urgencyLevel === 'critical' ? theme.palette.error.main :
            urgencyLevel === 'high' ? theme.palette.warning.main :
            theme.palette.info.main
          }`,
          borderRadius: 2,
          position: 'relative'
        }}
      >
        {/* Close button */}
        <IconButton
          size="small"
          onClick={handleMinimize}
          sx={{ position: 'absolute', top: 8, right: 8 }}
        >
          <CloseIcon />
        </IconButton>
        
        <Stack spacing={2}>
          {/* Header */}
          <Stack direction={{ xs: 'column', md: 'row' }} spacing={2} alignItems={{ md: 'center' }} justifyContent="space-between">
            <Box>
              <Stack direction="row" spacing={1} alignItems="center" mb={1}>
                {urgencyIcon}
                <Typography variant="h6" fontWeight="bold" color={`${urgencyColor}.main`}>
                  {planId} Plan Trial
                </Typography>
                <Chip 
                  label={`${daysRemaining}d ${hoursRemaining}h left`} 
                  size="small" 
                  color={urgencyColor}
                  variant="filled"
                />
              </Stack>
              
              <Typography variant="body1" color="text.primary" gutterBottom>
                {getUrgencyMessage()}
              </Typography>
            </Box>
            
            <Stack direction="row" spacing={1}>
              {daysRemaining <= 7 && (
                <Button
                  variant="outlined"
                  size="small"
                  color="inherit"
                  onClick={handleExtensionRequest}
                >
                  Request Extension
                </Button>
              )}
              
              <Button
                variant="outlined"
                size="small"
                color="inherit"
                onClick={handleCancel}
              >
                Cancel Trial
              </Button>
              
              <Button
                variant="contained"
                size="large"
                color={urgencyColor}
                onClick={handleUpgrade}
                disabled={isConverting}
                startIcon={<UpgradeIcon />}
                sx={{ minWidth: 120 }}
              >
                {isConverting ? 'Upgrading...' : 'Upgrade Now'}
              </Button>
            </Stack>
          </Stack>
          
          {/* Progress bar */}
          {showProgress && (
            <Box>
              <Stack direction="row" justifyContent="space-between" alignItems="center" mb={1}>
                <Typography variant="body2" color="text.secondary">
                  Trial Progress
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {daysUsed} of {totalDays} days used
                </Typography>
              </Stack>
              <LinearProgress 
                variant="determinate" 
                value={progress} 
                color={urgencyColor}
                sx={{ height: 8, borderRadius: 1 }}
              />
            </Box>
          )}
          
          {/* Features highlight */}
          {showFeatures && (
            <Stack direction="row" spacing={2} alignItems="center">
              <Typography variant="body2" color="text.secondary">
                You&apos;re enjoying:
              </Typography>
              <Chip label="200 posts/month" size="small" variant="outlined" />
              <Chip label="Advanced AI tools" size="small" variant="outlined" />
              <Chip label="Team collaboration" size="small" variant="outlined" />
              <Button
                size="small"
                onClick={() => setShowFeatureDialog(true)}
                sx={{ textTransform: 'none' }}
              >
                See all features
              </Button>
            </Stack>
          )}
          
          {/* Help links */}
          <Typography variant="caption" color="text.secondary">
            Need help deciding? <Link component={RouterLink} to="/billing/plans" color="primary">Compare plans</Link> or <Link component={RouterLink} to="/support" color="primary">contact support</Link>.
          </Typography>
        </Stack>
      </Paper>
      
      {/* Features dialog */}
      <Dialog
        open={showFeatureDialog}
        onClose={() => setShowFeatureDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Your Accelerator Trial Features
        </DialogTitle>
        <DialogContent>
          <List>
            {[
              '200 posts per month',
              'Advanced AI content generation',
              'Team collaboration (5 users)',
              '100 regeneration credits',
              'Advanced analytics (90 days)',
              'Priority customer support',
              'Brand voice consistency',
              'Policy compliance checking'
            ].map((feature, index) => (
              <ListItem key={index}>
                <ListItemIcon>
                  <CheckCircleIcon color="success" />
                </ListItemIcon>
                <ListItemText primary={feature} />
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowFeatureDialog(false)}>
            Close
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              setShowFeatureDialog(false);
              handleUpgrade();
            }}
            startIcon={<UpgradeIcon />}
          >
            Upgrade Now
          </Button>
        </DialogActions>
      </Dialog>

      {/* Extension Dialog */}
      <Dialog
        open={showExtensionDialog}
        onClose={() => setShowExtensionDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Request Trial Extension
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            Need more time to evaluate our premium features? Our support team can help you with a possible trial extension.
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Extensions are granted on a case-by-case basis and typically provide an additional 7 days to explore premium features.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowExtensionDialog(false)}>
            Not Now
          </Button>
          <Button
            variant="contained"
            onClick={handleExtensionConfirm}
            startIcon={<SupportIcon />}
          >
            Contact Support
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}));

EnhancedTrialBanner.displayName = 'EnhancedTrialBanner';

EnhancedTrialBanner.propTypes = {
  /** Banner display variant */
  variant: PropTypes.oneOf(Object.values(BANNER_VARIANTS)),
  /** Show feature highlights */
  showFeatures: PropTypes.bool,
  /** Show trial progress bar */
  showProgress: PropTypes.bool,
  /** Auto-hide banner after delay */
  autoHide: PropTypes.bool,
  /** Banner position */
  position: PropTypes.oneOf(Object.values(BANNER_POSITIONS)),
  /** Enable accessibility features */
  enableAccessibility: PropTypes.bool,
  /** Enable conversion tracking */
  enableConversionTracking: PropTypes.bool,
  /** Show live countdown */
  showCountdown: PropTypes.bool,
  /** Trial action callback */
  onTrialAction: PropTypes.func,
  /** Analytics tracking callback */
  onAnalyticsTrack: PropTypes.func,
  /** Conversion event callback */
  onConversionEvent: PropTypes.func
};

export default EnhancedTrialBanner;
