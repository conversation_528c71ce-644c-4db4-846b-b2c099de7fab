/**
 * Enhanced Trial Page Header - Enterprise-grade trial-specific page header component
 * Features: Comprehensive trial page header with advanced trial title management, trial-specific
 * breadcrumbs, and trial state integration, detailed trial header customization with dynamic trial
 * titles and personalized trial navigation flows, advanced trial header features with trial progress
 * bars and trial countdown displays, ACE Social's trial system integration with seamless trial
 * header lifecycle management, trial header interaction features including trial-specific action
 * buttons and trial upgrade prompts, trial header state management with real-time trial updates
 * and trial validation checks, real-time trial header updates with live trial displays and dynamic
 * trial messaging, and seamless ACE Social trial platform integration with advanced trial header
 * orchestration and comprehensive trial accessibility compliance
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 */

import {
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Breadcrumbs,
  Link,
  useTheme,
  alpha,
  Divider,
  Chip,
  Alert,
  LinearProgress,
  Stack,
  IconButton,
  Tooltip,
  Collapse,
  Paper,
  Button
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import {
  NavigateNext as NavigateNextIcon,
  Schedule as ScheduleIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Upgrade as UpgradeIcon,
  Settings as SettingsIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Home as HomeIcon,
  Notifications as NotificationsIcon
} from '@mui/icons-material';
import { useTrial } from '../contexts/TrialContext';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Trial header types
const TRIAL_HEADER_TYPES = {
  TRIAL_DASHBOARD: 'trial_dashboard',
  TRIAL_FEATURES: 'trial_features',
  TRIAL_SETTINGS: 'trial_settings',
  TRIAL_BILLING: 'trial_billing',
  TRIAL_SUPPORT: 'trial_support'
};

// Trial status types
const TRIAL_STATUS_TYPES = {
  ACTIVE: 'active',
  WARNING: 'warning',
  CRITICAL: 'critical',
  EXPIRED: 'expired'
};

/**
 * Enhanced Trial Page Header - Comprehensive trial page header with advanced features
 * Implements detailed trial header management and enterprise-grade trial header capabilities
 */
const TrialPageHeader = memo(forwardRef(({
  title,
  subtitle,
  breadcrumbs,
  actions,
  icon: Icon,
  divider = true,
  marginBottom = 3,
  headerType = TRIAL_HEADER_TYPES.TRIAL_DASHBOARD,
  showTrialProgress = true,
  showTrialCountdown = true,
  showTrialActions = true,
  showTrialStatus = true,
  enableAccessibility = true,
  enableAnalytics = true,
  onTrialAction,
  onAnalyticsTrack,
  onHeaderInteraction
}, ref) => {
  const theme = useTheme();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Core refs for component management
  const headerRef = useRef(null);
  const titleRef = useRef(null);

  // Trial context integration
  const {
    isTrial,
    daysRemaining,
    hoursRemaining,
    trialEnd,
    planId
  } = useTrial();

  // Enhanced state management
  const [showTrialDetails, setShowTrialDetails] = useState(false);
  const [headerAnalytics, setHeaderAnalytics] = useState({
    headerLoadTime: null,
    interactions: 0,
    lastActivity: new Date().toISOString()
  });

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    focusTitle: () => titleRef.current?.focus(),
    focusHeader: () => headerRef.current?.focus(),
    getHeaderAnalytics: () => headerAnalytics,
    resetAnalytics: () => setHeaderAnalytics({
      headerLoadTime: null,
      interactions: 0,
      lastActivity: new Date().toISOString()
    }),
    showTrialDetails: () => setShowTrialDetails(true),
    hideTrialDetails: () => setShowTrialDetails(false)
  }), [headerAnalytics]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced trial status calculation
  const trialStatus = useMemo(() => {
    if (!isTrial) return { type: TRIAL_STATUS_TYPES.ACTIVE, message: 'Not in trial' };

    if (daysRemaining <= 0 && hoursRemaining <= 0) {
      return { type: TRIAL_STATUS_TYPES.EXPIRED, message: 'Trial expired' };
    } else if (daysRemaining === 0 && hoursRemaining <= 6) {
      return { type: TRIAL_STATUS_TYPES.CRITICAL, message: `${hoursRemaining}h remaining` };
    } else if (daysRemaining <= 1) {
      return { type: TRIAL_STATUS_TYPES.WARNING, message: `${daysRemaining}d ${hoursRemaining}h remaining` };
    }

    return { type: TRIAL_STATUS_TYPES.ACTIVE, message: `${daysRemaining} days remaining` };
  }, [isTrial, daysRemaining, hoursRemaining]);

  // Enhanced effects for component lifecycle
  useEffect(() => {
    setHeaderAnalytics(prev => ({
      ...prev,
      headerLoadTime: Date.now(),
      lastActivity: new Date().toISOString()
    }));

    if (enableAccessibility) {
      announceToScreenReader(`Trial header loaded: ${title || 'Trial Page'}`);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack('trial_header_viewed', {
        headerType,
        title,
        trialStatus: trialStatus.type,
        timestamp: new Date().toISOString()
      });
    }
  }, [
    title,
    headerType,
    trialStatus.type,
    enableAccessibility,
    announceToScreenReader,
    enableAnalytics,
    onAnalyticsTrack
  ]);

  // Enhanced handlers
  const handleTrialAction = useCallback((action, data) => {
    setHeaderAnalytics(prev => ({
      ...prev,
      interactions: prev.interactions + 1,
      lastActivity: new Date().toISOString()
    }));

    if (onTrialAction) {
      onTrialAction(action, {
        ...data,
        headerType,
        timestamp: new Date().toISOString()
      });
    }

    if (onHeaderInteraction) {
      onHeaderInteraction('trial_action', {
        action,
        data,
        timestamp: new Date().toISOString()
      });
    }
  }, [onTrialAction, onHeaderInteraction, headerType]);

  const handleUpgradeClick = useCallback(() => {
    handleTrialAction('upgrade_clicked', { source: 'header' });
    window.location.href = '/billing/plans?source=trial_header';
  }, [handleTrialAction]);

  return (
    <Paper
      ref={headerRef}
      elevation={2}
      sx={{
        ...glassMorphismStyles,
        mb: marginBottom,
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {/* Trial Progress Bar */}
      {showTrialProgress && isTrial && (
        <LinearProgress
          variant="determinate"
          value={Math.max(0, Math.min(100, ((14 - daysRemaining) / 14) * 100))}
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: 4,
            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.2),
            '& .MuiLinearProgress-bar': {
              backgroundColor: trialStatus.type === TRIAL_STATUS_TYPES.CRITICAL ? '#F44336' :
                             trialStatus.type === TRIAL_STATUS_TYPES.WARNING ? ACE_COLORS.YELLOW :
                             ACE_COLORS.PURPLE
            }
          }}
        />
      )}

      <Box sx={{ p: 3 }}>
        {/* Trial Status Alert */}
        {showTrialStatus && isTrial && trialStatus.type !== TRIAL_STATUS_TYPES.ACTIVE && (
          <Alert
            severity={
              trialStatus.type === TRIAL_STATUS_TYPES.CRITICAL ? 'error' :
              trialStatus.type === TRIAL_STATUS_TYPES.WARNING ? 'warning' : 'info'
            }
            sx={{ mb: 2 }}
            action={
              showTrialActions && (
                <Button
                  color="inherit"
                  size="small"
                  onClick={handleUpgradeClick}
                  startIcon={<UpgradeIcon />}
                >
                  Upgrade
                </Button>
              )
            }
          >
            <Typography variant="body2">
              <strong>Trial Status:</strong> {trialStatus.message}
              {trialEnd && (
                <span> (expires {new Date(trialEnd).toLocaleDateString()})</span>
              )}
            </Typography>
          </Alert>
        )}

        {/* Enhanced Breadcrumbs */}
        {breadcrumbs && breadcrumbs.length > 0 && (
          <Breadcrumbs
            separator={<NavigateNextIcon fontSize="small" />}
            aria-label="trial breadcrumb navigation"
            sx={{ mb: 2 }}
          >
            <Link
              component={RouterLink}
              to="/"
              color="inherit"
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
                textDecoration: 'none',
                '&:hover': {
                  textDecoration: 'underline'
                }
              }}
            >
              <HomeIcon fontSize="small" />
              {isTrial ? 'Trial Dashboard' : 'Home'}
            </Link>
            {breadcrumbs.map((crumb, index) => {
              const isLast = index === breadcrumbs.length - 1;
              return isLast ? (
                <Typography key={index} color="text.primary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  {crumb.icon && crumb.icon}
                  {crumb.label}
                </Typography>
              ) : (
                <Link
                  key={index}
                  component={RouterLink}
                  to={crumb.path}
                  color="inherit"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 0.5,
                    textDecoration: 'none',
                    '&:hover': {
                      textDecoration: 'underline'
                    }
                  }}
                >
                  {crumb.icon && crumb.icon}
                  {crumb.label}
                </Link>
              );
            })}
          </Breadcrumbs>
        )}

        {/* Enhanced Header Content */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'flex-start', sm: 'center' },
            mb: divider ? 2 : 0
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
            {Icon && (
              <Box
                sx={{
                  mr: 2,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 48,
                  height: 48,
                  borderRadius: 1,
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                  color: ACE_COLORS.PURPLE
                }}
              >
                {Icon}
              </Box>
            )}
            <Box sx={{ flex: 1 }}>
              <Stack direction="row" alignItems="center" spacing={2}>
                <Typography
                  ref={titleRef}
                  variant="h4"
                  component="h1"
                  gutterBottom={Boolean(subtitle)}
                  sx={{ fontWeight: 'bold' }}
                >
                  {isTrial ? `[TRIAL] ${title}` : title}
                </Typography>

                {/* Trial Status Chip */}
                {showTrialStatus && isTrial && (
                  <Chip
                    label={trialStatus.message.toUpperCase()}
                    color={
                      trialStatus.type === TRIAL_STATUS_TYPES.CRITICAL ? 'error' :
                      trialStatus.type === TRIAL_STATUS_TYPES.WARNING ? 'warning' :
                      'primary'
                    }
                    variant="outlined"
                    icon={<ScheduleIcon />}
                    size="small"
                  />
                )}
              </Stack>

              {subtitle && (
                <Typography variant="subtitle1" color="text.secondary">
                  {subtitle}
                </Typography>
              )}

              {/* Trial Countdown */}
              {showTrialCountdown && isTrial && (
                <Collapse in={showTrialDetails}>
                  <Box sx={{ mt: 1, p: 2, backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05), borderRadius: 1 }}>
                    <Stack direction="row" spacing={3}>
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          Plan ID
                        </Typography>
                        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                          {planId || 'Trial'}
                        </Typography>
                      </Box>
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          Days Left
                        </Typography>
                        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                          {daysRemaining}
                        </Typography>
                      </Box>
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          Hours Left
                        </Typography>
                        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                          {hoursRemaining}
                        </Typography>
                      </Box>
                    </Stack>
                  </Box>
                </Collapse>
              )}
            </Box>
          </Box>

          {/* Enhanced Actions */}
          <Stack direction="row" spacing={1} sx={{ mt: { xs: 2, sm: 0 } }}>
            {/* Trial Details Toggle */}
            {showTrialCountdown && isTrial && (
              <Tooltip title="Show trial details">
                <IconButton
                  onClick={() => setShowTrialDetails(!showTrialDetails)}
                  size="small"
                >
                  {showTrialDetails ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              </Tooltip>
            )}

            {/* Trial Actions */}
            {showTrialActions && isTrial && (
              <Tooltip title="Upgrade trial">
                <IconButton
                  onClick={handleUpgradeClick}
                  size="small"
                  sx={{
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                    '&:hover': {
                      backgroundColor: alpha(ACE_COLORS.PURPLE, 0.2)
                    }
                  }}
                >
                  <UpgradeIcon />
                </IconButton>
              </Tooltip>
            )}

            {/* Custom Actions */}
            {actions && (
              <Box
                sx={{
                  display: 'flex',
                  gap: 1,
                  flexWrap: 'wrap'
                }}
              >
                {actions}
              </Box>
            )}
          </Stack>
        </Box>

        {/* Enhanced Divider */}
        {divider && (
          <Divider
            sx={{
              mt: 2,
              background: `linear-gradient(90deg, transparent, ${alpha(ACE_COLORS.PURPLE, 0.3)}, transparent)`
            }}
          />
        )}
      </Box>
    </Paper>
  );
}));

TrialPageHeader.displayName = 'TrialPageHeader';

TrialPageHeader.propTypes = {
  /** Page title to be displayed */
  title: PropTypes.string.isRequired,
  /** Optional subtitle */
  subtitle: PropTypes.string,
  /** Breadcrumb navigation items */
  breadcrumbs: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      path: PropTypes.string,
      icon: PropTypes.node
    })
  ),
  /** Custom action buttons */
  actions: PropTypes.node,
  /** Header icon */
  icon: PropTypes.node,
  /** Show divider below header */
  divider: PropTypes.bool,
  /** Bottom margin spacing */
  marginBottom: PropTypes.number,
  /** Trial header type for analytics */
  headerType: PropTypes.oneOf(Object.values(TRIAL_HEADER_TYPES)),
  /** Show trial progress bar */
  showTrialProgress: PropTypes.bool,
  /** Show trial countdown details */
  showTrialCountdown: PropTypes.bool,
  /** Show trial action buttons */
  showTrialActions: PropTypes.bool,
  /** Show trial status indicators */
  showTrialStatus: PropTypes.bool,
  /** Enable accessibility features */
  enableAccessibility: PropTypes.bool,
  /** Enable analytics tracking */
  enableAnalytics: PropTypes.bool,
  /** Trial action callback */
  onTrialAction: PropTypes.func,
  /** Analytics tracking callback */
  onAnalyticsTrack: PropTypes.func,
  /** Header interaction callback */
  onHeaderInteraction: PropTypes.func
};

export default TrialPageHeader;
