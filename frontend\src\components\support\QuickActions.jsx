/**
 * Enhanced Quick Actions - Enterprise-grade quick actions component
 * Features: Comprehensive quick actions system with customizable action buttons and workflow shortcuts,
 * detailed action categorization with support workflows and common tasks, advanced quick action features
 * with keyboard shortcuts and action history, ACE Social's support system integration with seamless
 * navigation to tickets and chat, quick action interaction features including action analytics and
 * usage tracking, action customization capabilities with user preferences and personalized quick access,
 * real-time action updates with dynamic action availability and context-aware action suggestions,
 * and seamless ACE Social platform integration with advanced quick actions management
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 */

import {
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
  useCallback,
  useMemo,
  useRef
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  IconButton,
  Chip,
  useTheme,
  alpha,
  Tooltip,
  Badge,
  Fade,
  Zoom,
  Menu,
  MenuItem,
  Divider,
  LinearProgress,
  Avatar,
  Stack
} from '@mui/material';
import {
  Add as AddIcon,
  Chat as ChatIcon,
  Help as HelpIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Schedule as ScheduleIcon,
  History as HistoryIcon,
  Star as StarIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  QuestionAnswer as QAIcon,
  ContactSupport as ContactIcon,
  School as TrainingIcon
} from '@mui/icons-material';
import { useNotification } from '../../hooks/useNotification';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Action categories
const ACTION_CATEGORIES = {
  SUPPORT: 'support',
  HELP: 'help',
  CONTACT: 'contact',
  RESOURCES: 'resources',
  ACCOUNT: 'account'
};

// Action priority levels
const ACTION_PRIORITY = {
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low'
};

/**
 * Enhanced Quick Actions - Comprehensive quick actions with advanced features
 * Implements customizable workflows and enterprise-grade action management capabilities
 */
const QuickActions = memo(forwardRef(({
  onCreateTicket,
  onStartChat,
  onBrowseHelp,
  onContactSupport,
  enableActionHistory = true,
  enableActionAnalytics = true,
  maxRecentActions = 5,
  onActionExecuted,
  onActionAnalytics,
  showActionTooltips = true
}, ref) => {
  const theme = useTheme();
  const { showSuccessNotification, showErrorNotification } = useNotification();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Enhanced state management
  const [actionHistory, setActionHistory] = useState([]);
  const [actionAnalytics, setActionAnalytics] = useState({});
  const [recentActions, setRecentActions] = useState([]);
  const [favoriteActions, setFavoriteActions] = useState([]);
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(ACTION_CATEGORIES.SUPPORT);
  const [loading, setLoading] = useState(false);

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    executeAction: (actionId) => handleActionExecute(actionId),
    getActionHistory: () => actionHistory,
    clearActionHistory: () => setActionHistory([]),
    getActionAnalytics: () => actionAnalytics,
    getFavoriteActions: () => favoriteActions,
    addFavoriteAction: (actionId) => handleAddFavorite(actionId),
    removeFavoriteAction: (actionId) => handleRemoveFavorite(actionId),
    getRecentActions: () => recentActions,
    refreshActions: () => handleRefreshActions(),
    setCategory: (category) => setSelectedCategory(category)
  }), [
    actionHistory,
    actionAnalytics,
    favoriteActions,
    recentActions,
    handleActionExecute,
    handleAddFavorite,
    handleRemoveFavorite,
    handleRefreshActions
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced quick actions with comprehensive features
  const quickActions = useMemo(() => [
    {
      id: 'create_ticket',
      title: 'Create Ticket',
      description: 'Submit a new support request',
      icon: <AddIcon />,
      color: ACE_COLORS.PURPLE,
      category: ACTION_CATEGORIES.SUPPORT,
      priority: ACTION_PRIORITY.HIGH,
      action: onCreateTicket,
      shortcut: 'Ctrl+T',
      analytics: { clicks: 0, lastUsed: null }
    },
    {
      id: 'live_chat',
      title: 'Live Chat',
      description: 'Chat with support agent',
      icon: <ChatIcon />,
      color: ACE_COLORS.YELLOW,
      category: ACTION_CATEGORIES.SUPPORT,
      priority: ACTION_PRIORITY.HIGH,
      action: onStartChat,
      shortcut: 'Ctrl+C',
      analytics: { clicks: 0, lastUsed: null }
    },
    {
      id: 'browse_help',
      title: 'Browse Help',
      description: 'Search knowledge base',
      icon: <HelpIcon />,
      color: ACE_COLORS.DARK,
      category: ACTION_CATEGORIES.HELP,
      priority: ACTION_PRIORITY.MEDIUM,
      action: onBrowseHelp || (() => console.log('Browse help')),
      shortcut: 'Ctrl+H',
      analytics: { clicks: 0, lastUsed: null }
    },
    {
      id: 'contact_support',
      title: 'Contact Support',
      description: 'Get in touch with our team',
      icon: <ContactIcon />,
      color: ACE_COLORS.PURPLE,
      category: ACTION_CATEGORIES.CONTACT,
      priority: ACTION_PRIORITY.MEDIUM,
      action: onContactSupport || (() => console.log('Contact support')),
      shortcut: 'Ctrl+S',
      analytics: { clicks: 0, lastUsed: null }
    },
    {
      id: 'download_resources',
      title: 'Download Resources',
      description: 'Access guides and documentation',
      icon: <DownloadIcon />,
      color: ACE_COLORS.YELLOW,
      category: ACTION_CATEGORIES.RESOURCES,
      priority: ACTION_PRIORITY.LOW,
      action: () => console.log('Download resources'),
      shortcut: 'Ctrl+D',
      analytics: { clicks: 0, lastUsed: null }
    },
    {
      id: 'training_center',
      title: 'Training Center',
      description: 'Learn how to use ACE Social',
      icon: <TrainingIcon />,
      color: ACE_COLORS.PURPLE,
      category: ACTION_CATEGORIES.RESOURCES,
      priority: ACTION_PRIORITY.LOW,
      action: () => console.log('Training center'),
      shortcut: 'Ctrl+L',
      analytics: { clicks: 0, lastUsed: null }
    }
  ], [onCreateTicket, onStartChat, onBrowseHelp, onContactSupport]);

  // Enhanced event handlers
  const handleActionExecute = useCallback(async (actionId) => {
    try {
      setLoading(true);

      const action = quickActions.find(a => a.id === actionId);
      if (!action) {
        showErrorNotification('Action not found');
        return;
      }

      // Track action analytics
      if (enableActionAnalytics) {
        setActionAnalytics(prev => ({
          ...prev,
          [actionId]: {
            ...prev[actionId],
            clicks: (prev[actionId]?.clicks || 0) + 1,
            lastUsed: new Date().toISOString()
          }
        }));

        if (onActionAnalytics) {
          onActionAnalytics({ actionId, timestamp: new Date().toISOString() });
        }
      }

      // Add to action history
      if (enableActionHistory) {
        setActionHistory(prev => [
          { actionId, timestamp: new Date().toISOString(), title: action.title },
          ...prev.slice(0, maxRecentActions - 1)
        ]);
      }

      // Update recent actions
      setRecentActions(prev => {
        const filtered = prev.filter(id => id !== actionId);
        return [actionId, ...filtered].slice(0, maxRecentActions);
      });

      // Execute the action
      if (action.action) {
        await action.action();
      }

      if (onActionExecuted) {
        onActionExecuted(actionId);
      }

      announceToScreenReader(`${action.title} action executed`);
      showSuccessNotification(`${action.title} completed`);
    } catch (error) {
      showErrorNotification(`Failed to execute action: ${error.message}`);
      announceToScreenReader('Action failed');
    } finally {
      setLoading(false);
    }
  }, [
    quickActions,
    enableActionAnalytics,
    enableActionHistory,
    maxRecentActions,
    onActionAnalytics,
    onActionExecuted,
    announceToScreenReader,
    showSuccessNotification,
    showErrorNotification
  ]);

  const handleAddFavorite = useCallback((actionId) => {
    setFavoriteActions(prev => {
      if (prev.includes(actionId)) return prev;
      const newFavorites = [...prev, actionId];
      announceToScreenReader('Action added to favorites');
      showSuccessNotification('Added to favorites');
      return newFavorites;
    });
  }, [announceToScreenReader, showSuccessNotification]);

  const handleRemoveFavorite = useCallback((actionId) => {
    setFavoriteActions(prev => {
      const newFavorites = prev.filter(id => id !== actionId);
      announceToScreenReader('Action removed from favorites');
      showSuccessNotification('Removed from favorites');
      return newFavorites;
    });
  }, [announceToScreenReader, showSuccessNotification]);

  const handleRefreshActions = useCallback(() => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      announceToScreenReader('Actions refreshed');
      showSuccessNotification('Actions updated');
    }, 1000);
  }, [announceToScreenReader, showSuccessNotification]);

  // Enhanced contact methods (no plan limitations)
  const contactMethods = useMemo(() => [
    {
      id: 'live_chat',
      method: 'Live Chat',
      availability: 'Available 24/7',
      icon: <ChatIcon />,
      color: ACE_COLORS.YELLOW,
      disabled: false,
      description: 'Instant support via live chat'
    },
    {
      id: 'email_support',
      method: 'Email Support',
      availability: '2-4h response',
      icon: <EmailIcon />,
      color: ACE_COLORS.PURPLE,
      disabled: false,
      description: 'Comprehensive support via email'
    },
    {
      id: 'phone_support',
      method: 'Phone Support',
      availability: 'Business hours',
      icon: <PhoneIcon />,
      color: ACE_COLORS.DARK,
      disabled: false,
      description: 'Direct phone support available'
    },
    {
      id: 'community_forum',
      method: 'Community Forum',
      availability: 'Always available',
      icon: <QAIcon />,
      color: ACE_COLORS.PURPLE,
      disabled: false,
      description: 'Connect with other users'
    }
  ], []);

  // Filter actions by category
  const filteredActions = useMemo(() => {
    return quickActions.filter(action =>
      selectedCategory === ACTION_CATEGORIES.SUPPORT || action.category === selectedCategory
    );
  }, [quickActions, selectedCategory]);

  return (
    <Box sx={{ ...glassMorphismStyles, p: 3 }}>
      {/* Enhanced Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box>
          <Typography variant="h5" gutterBottom sx={{
            fontWeight: 700,
            color: ACE_COLORS.DARK,
            background: `linear-gradient(135deg, ${ACE_COLORS.PURPLE} 0%, ${ACE_COLORS.YELLOW} 100%)`,
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}>
            How can we help you today?
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Choose from our quick actions or browse support options below
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="Action History">
            <IconButton
              onClick={(e) => setMenuAnchorEl(e.currentTarget)}
              sx={{ color: ACE_COLORS.PURPLE }}
            >
              <Badge badgeContent={actionHistory.length} color="primary">
                <HistoryIcon />
              </Badge>
            </IconButton>
          </Tooltip>
          <Tooltip title="Refresh Actions">
            <IconButton
              onClick={handleRefreshActions}
              disabled={loading}
              sx={{ color: ACE_COLORS.PURPLE }}
            >
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Category Filter */}
      <Box sx={{ mb: 3 }}>
        <Stack direction="row" spacing={1} flexWrap="wrap">
          {Object.values(ACTION_CATEGORIES).map((category) => (
            <Chip
              key={category}
              label={category.charAt(0).toUpperCase() + category.slice(1)}
              onClick={() => setSelectedCategory(category)}
              variant={selectedCategory === category ? 'filled' : 'outlined'}
              sx={{
                backgroundColor: selectedCategory === category ? ACE_COLORS.PURPLE : 'transparent',
                color: selectedCategory === category ? ACE_COLORS.WHITE : ACE_COLORS.PURPLE,
                borderColor: ACE_COLORS.PURPLE,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                }
              }}
            />
          ))}
        </Stack>
      </Box>

      {/* Loading Progress */}
      {loading && (
        <Box sx={{ mb: 2 }}>
          <LinearProgress
            sx={{
              backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
              '& .MuiLinearProgress-bar': {
                backgroundColor: ACE_COLORS.PURPLE
              }
            }}
          />
        </Box>
      )}

      {/* Enhanced Quick Actions */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {filteredActions.map((action) => (
          <Grid item xs={12} sm={6} md={4} key={action.id}>
            <Zoom in timeout={300}>
              <Card
                sx={{
                  cursor: 'pointer',
                  transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',
                  position: 'relative',
                  overflow: 'visible',
                  '&:hover': {
                    transform: 'translateY(-8px) scale(1.02)',
                    boxShadow: `0 12px 40px ${alpha(action.color, 0.3)}`,
                  },
                  border: `2px solid ${alpha(action.color, 0.2)}`,
                  borderRadius: 3,
                  background: `linear-gradient(135deg,
                    ${alpha(theme.palette.background.paper, 0.95)} 0%,
                    ${alpha(action.color, 0.05)} 100%)`,
                  backdropFilter: 'blur(10px)'
                }}
                onClick={() => handleActionExecute(action.id)}
                role="button"
                tabIndex={0}
                aria-label={`${action.title}: ${action.description}`}
                onKeyPress={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    handleActionExecute(action.id);
                  }
                }}
              >
                {/* Favorite Badge */}
                {favoriteActions.includes(action.id) && (
                  <Box
                    sx={{
                      position: 'absolute',
                      top: -8,
                      right: -8,
                      zIndex: 1
                    }}
                  >
                    <Avatar
                      sx={{
                        width: 24,
                        height: 24,
                        backgroundColor: ACE_COLORS.YELLOW,
                        color: ACE_COLORS.WHITE
                      }}
                    >
                      <StarIcon sx={{ fontSize: 14 }} />
                    </Avatar>
                  </Box>
                )}

                <CardContent sx={{ textAlign: 'center', py: 3 }}>
                  <Box
                    sx={{
                      width: 64,
                      height: 64,
                      borderRadius: '50%',
                      backgroundColor: alpha(action.color, 0.1),
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      margin: '0 auto 16px',
                      border: `2px solid ${alpha(action.color, 0.3)}`,
                      transition: 'all 300ms ease'
                    }}
                  >
                    <Box sx={{ color: action.color, fontSize: 28 }}>
                      {action.icon}
                    </Box>
                  </Box>

                  <Typography variant="h6" fontWeight="bold" gutterBottom sx={{ color: ACE_COLORS.DARK }}>
                    {action.title}
                  </Typography>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {action.description}
                  </Typography>

                  {showActionTooltips && action.shortcut && (
                    <Chip
                      label={action.shortcut}
                      size="small"
                      variant="outlined"
                      sx={{
                        borderColor: alpha(action.color, 0.3),
                        color: action.color,
                        fontSize: '0.7rem'
                      }}
                    />
                  )}
                </CardContent>
              </Card>
            </Zoom>
          </Grid>
        ))}
      </Grid>

      {/* Enhanced Support Information */}
      <Card sx={{ ...glassMorphismStyles, border: `2px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}` }}>
        <CardContent sx={{ p: 3 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Box>
              <Typography variant="h6" fontWeight="bold" sx={{ color: ACE_COLORS.DARK }}>
                Support Availability
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Multiple ways to get help when you need it
              </Typography>
            </Box>
            <Chip
              label="24/7 Support"
              sx={{
                backgroundColor: ACE_COLORS.YELLOW,
                color: ACE_COLORS.DARK,
                fontWeight: 'bold'
              }}
            />
          </Box>

          <Grid container spacing={3}>
            {contactMethods.map((method) => (
              <Grid item xs={12} sm={6} md={3} key={method.id}>
                <Fade in timeout={500}>
                  <Card
                    sx={{
                      p: 2,
                      textAlign: 'center',
                      border: `1px solid ${alpha(method.color, 0.2)}`,
                      background: `linear-gradient(135deg,
                        ${alpha(theme.palette.background.paper, 0.8)} 0%,
                        ${alpha(method.color, 0.05)} 100%)`,
                      transition: 'all 300ms ease',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: `0 8px 25px ${alpha(method.color, 0.2)}`
                      }
                    }}
                  >
                    <Box
                      sx={{
                        width: 48,
                        height: 48,
                        borderRadius: '50%',
                        backgroundColor: alpha(method.color, 0.1),
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        margin: '0 auto 12px',
                        border: `2px solid ${alpha(method.color, 0.3)}`
                      }}
                    >
                      <Box sx={{ color: method.color, fontSize: 24 }}>
                        {method.icon}
                      </Box>
                    </Box>

                    <Typography variant="subtitle2" fontWeight="bold" gutterBottom sx={{ color: ACE_COLORS.DARK }}>
                      {method.method}
                    </Typography>

                    <Typography variant="caption" color="text.secondary" display="block" sx={{ mb: 1 }}>
                      {method.availability}
                    </Typography>

                    <Typography variant="caption" color="text.secondary">
                      {method.description}
                    </Typography>
                  </Card>
                </Fade>
              </Grid>
            ))}
          </Grid>

          {/* Enhanced SLA Information */}
          <Box sx={{ mt: 3, pt: 3, borderTop: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}` }}>
            <Box display="flex" alignItems="center" gap={2} mb={2}>
              <Avatar sx={{ backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1), color: ACE_COLORS.PURPLE }}>
                <ScheduleIcon />
              </Avatar>
              <Box>
                <Typography variant="subtitle1" fontWeight="bold" sx={{ color: ACE_COLORS.DARK }}>
                  Response Time Guarantee
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Fast, reliable support when you need it most
                </Typography>
              </Box>
            </Box>

            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              We&apos;ll respond to your support requests within <strong>2-4 hours</strong> during business hours.
              Priority support available 24/7 for urgent issues.
            </Typography>

            <Box sx={{
              p: 2,
              borderRadius: 2,
              backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
              border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`
            }}>
              <Typography variant="caption" color="text.secondary" display="block">
                <strong>Business Hours:</strong> Monday - Friday, 9:00 AM - 6:00 PM (UTC)
              </Typography>
              <Typography variant="caption" color="text.secondary">
                <strong>Emergency Support:</strong> Available 24/7 for critical issues
              </Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Action History Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={() => setMenuAnchorEl(null)}
        PaperProps={{
          sx: {
            ...glassMorphismStyles,
            minWidth: 250
          }
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
            Recent Actions
          </Typography>
          {actionHistory.length === 0 ? (
            <Typography variant="caption" color="text.secondary">
              No recent actions
            </Typography>
          ) : (
            actionHistory.slice(0, 5).map((item, index) => (
              <Box key={index} sx={{ py: 1 }}>
                <Typography variant="body2" fontWeight="medium">
                  {item.title}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {new Date(item.timestamp).toLocaleTimeString()}
                </Typography>
              </Box>
            ))
          )}
        </Box>
        <Divider />
        <MenuItem onClick={() => setActionHistory([])}>
          Clear History
        </MenuItem>
      </Menu>
    </Box>
  );
}));

QuickActions.displayName = 'QuickActions';

QuickActions.propTypes = {
  /** Function called when create ticket is requested */
  onCreateTicket: PropTypes.func,
  /** Function called when live chat is started */
  onStartChat: PropTypes.func,
  /** Function called when browse help is requested */
  onBrowseHelp: PropTypes.func,
  /** Function called when contact support is requested */
  onContactSupport: PropTypes.func,
  /** Enable action history tracking */
  enableActionHistory: PropTypes.bool,
  /** Enable action analytics tracking */
  enableActionAnalytics: PropTypes.bool,
  /** Enable action customization */
  enableCustomization: PropTypes.bool,
  /** Maximum number of recent actions to track */
  maxRecentActions: PropTypes.number,
  /** Custom actions to add to the quick actions */
  customActions: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    description: PropTypes.string,
    icon: PropTypes.element,
    color: PropTypes.string,
    category: PropTypes.string,
    priority: PropTypes.string,
    action: PropTypes.func,
    shortcut: PropTypes.string
  })),
  /** User action preferences */
  actionPreferences: PropTypes.object,
  /** Function called when action is executed */
  onActionExecuted: PropTypes.func,
  /** Function called when action analytics are tracked */
  onActionAnalytics: PropTypes.func,
  /** Show action tooltips with shortcuts */
  showActionTooltips: PropTypes.bool,
  /** Enable compact mode for smaller displays */
  compactMode: PropTypes.bool
};

export default QuickActions;

export default QuickActions;
