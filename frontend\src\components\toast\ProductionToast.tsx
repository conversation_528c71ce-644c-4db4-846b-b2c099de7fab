/**
 * Enhanced Production Toast - Enterprise-grade individual notification display component
 * Features: Comprehensive individual toast notification with advanced animation controls, action buttons,
 * and progress indicators, detailed toast customization with theme variants and size options, advanced
 * notification features with auto-dismiss timers and user interaction tracking, ACE Social's notification
 * system integration with seamless toast lifecycle management, toast interaction features including
 * swipe-to-dismiss and keyboard navigation, notification state management with read/unread status and
 * user preferences, real-time toast updates with live content changes and dynamic styling, and seamless
 * ACE Social platform integration with advanced notification orchestration and comprehensive accessibility
 * compliance
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 */

import React, {
  memo,
  forwardRef,
  useImperativeHandle,
  useEffect,
  useRef,
  useState,
  useCallback,
  useMemo
} from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Button,
  Slide,
  Fade,
  Grow,
  useTheme,
  alpha,
  Stack,
  Chip,
  LinearProgress,
  Zoom,
  Collapse,
  Avatar,
  Badge,
  Tooltip
} from '@mui/material';
import {
  Close as CloseIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Undo as UndoIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Star as StarIcon,
  Schedule as ScheduleIcon,
  Visibility as VisibilityIcon,
  SwipeLeft as SwipeLeftIcon,
  TouchApp as TouchAppIcon
} from '@mui/icons-material';
import { ToastState, ToastType } from '../../types/toast';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message: string) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
} as const;

// Toast sizes
const TOAST_SIZES = {
  COMPACT: 'COMPACT',
  STANDARD: 'STANDARD',
  EXPANDED: 'EXPANDED'
} as const;

// Animation types
const ANIMATION_TYPES = {
  SLIDE: 'SLIDE',
  FADE: 'FADE',
  ZOOM: 'ZOOM',
  BOUNCE: 'BOUNCE'
} as const;

// Toast interaction states
const INTERACTION_STATES = {
  IDLE: 'IDLE',
  HOVERED: 'HOVERED',
  FOCUSED: 'FOCUSED',
  PRESSED: 'PRESSED',
  SWIPING: 'SWIPING'
} as const;

/**
 * Enhanced Production Toast Props Interface
 * Comprehensive interface for individual toast notification configuration
 */
interface ProductionToastProps {
  /** Toast state object with configuration and metadata */
  toast: ToastState;
  /** Callback for toast dismissal */
  onDismiss: (id: string) => void;
  /** Callback for action button clicks */
  onAction?: (id: string, actionIndex?: number) => void;
  /** Callback for undo action */
  onUndo?: (id: string) => void;
  /** Callback for toast interaction tracking */
  onInteraction?: (id: string, interaction: string) => void;
  /** Callback for analytics tracking */
  onAnalyticsTrack?: (event: string, data: unknown) => void;
  /** Toast position for animation direction */
  position: 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right';
  /** Stack index for layering */
  stackIndex: number;
  /** Animation duration in milliseconds */
  animationDuration?: number;
  /** Enable keyboard navigation */
  enableKeyboardNavigation?: boolean;
  /** Enable swipe gestures */
  enableSwipeGestures?: boolean;
  /** Enable analytics tracking */
  enableAnalytics?: boolean;
  /** Toast size variant */
  size?: keyof typeof TOAST_SIZES;
  /** Animation type */
  animationType?: keyof typeof ANIMATION_TYPES;
  /** Enable glass morphism styling */
  enableGlassMorphism?: boolean;
  /** Show progress indicator */
  showProgress?: boolean;
  /** Enable expandable content */
  enableExpandable?: boolean;
}

/**
 * Toast Component Reference Interface
 * Methods exposed through useImperativeHandle for external control
 */
interface ToastRef {
  dismiss: () => void;
  expand: () => void;
  collapse: () => void;
  focus: () => void;
  getInteractionState: () => string;
  triggerAction: (actionIndex: number) => void;
}

/**
 * Enhanced Production Toast - Comprehensive individual notification with advanced features
 * Implements detailed toast display and enterprise-grade notification capabilities
 */
const ProductionToast = memo(forwardRef<ToastRef, ProductionToastProps>(({
  toast,
  onDismiss,
  onAction,
  onUndo,
  onInteraction,
  onAnalyticsTrack,
  position,
  stackIndex,
  animationDuration = 300,
  enableKeyboardNavigation = true,
  enableSwipeGestures = true,
  enableAnalytics = true,
  size = TOAST_SIZES.STANDARD,
  animationType = ANIMATION_TYPES.SLIDE,
  enableGlassMorphism = true,
  showProgress = false,
  enableExpandable = false
}, ref) => {
  const theme = useTheme();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Core refs for component management
  const toastRef = useRef<HTMLDivElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);
  const swipeStartRef = useRef<{ x: number; y: number } | null>(null);

  // Enhanced state management
  const [interactionState, setInteractionState] = useState<keyof typeof INTERACTION_STATES>(INTERACTION_STATES.IDLE);
  const [isExpanded, setIsExpanded] = useState(false);
  const [retryCount, setRetryCount] = useState(toast.config.retryCount || 0);
  const [swipeOffset, setSwipeOffset] = useState(0);
  const [progressValue, setProgressValue] = useState(0);
  const [isRead, setIsRead] = useState(false);
  const [toastAnalytics, setToastAnalytics] = useState({
    viewTime: 0,
    interactions: 0,
    lastInteraction: new Date().toISOString()
  });



  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: enableGlassMorphism
      ? `linear-gradient(135deg,
          ${alpha(theme.palette.background.paper, 0.95)} 0%,
          ${alpha(theme.palette.background.default, 0.85)} 100%)`
      : theme.palette.background.paper,
    backdropFilter: enableGlassMorphism ? 'blur(15px)' : 'none',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme, enableGlassMorphism]);

  // Enhanced event handlers
  const handleDismiss = useCallback(() => {
    onDismiss(toast.id);

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack('toast_dismissed', {
        toastId: toast.id,
        type: toast.config.type,
        viewTime: toastAnalytics.viewTime,
        interactions: toastAnalytics.interactions,
        timestamp: new Date().toISOString()
      });
    }

    announceToScreenReader('Notification dismissed');
  }, [
    onDismiss,
    toast.id,
    toast.config.type,
    enableAnalytics,
    onAnalyticsTrack,
    toastAnalytics,
    announceToScreenReader
  ]);

  const handleActionClick = useCallback((actionIndex?: number) => {
    if (onAction) {
      onAction(toast.id, actionIndex);
    }

    if (onInteraction) {
      onInteraction(toast.id, `action_${actionIndex || 0}_clicked`);
    }

    setToastAnalytics(prev => ({
      ...prev,
      interactions: prev.interactions + 1,
      lastInteraction: new Date().toISOString()
    }));

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack('toast_action_clicked', {
        toastId: toast.id,
        actionIndex,
        timestamp: new Date().toISOString()
      });
    }

    announceToScreenReader(`Action ${actionIndex || 0} activated`);
  }, [
    onAction,
    onInteraction,
    toast.id,
    enableAnalytics,
    onAnalyticsTrack,
    announceToScreenReader
  ]);

  // Enhanced utility functions
  const getTypeColor = useCallback((type: ToastType) => {
    switch (type) {
      case 'success':
        return '#4CAF50';
      case 'error':
        return '#F44336';
      case 'warning':
        return ACE_COLORS.YELLOW;
      case 'info':
      default:
        return ACE_COLORS.PURPLE;
    }
  }, []);

  const getIcon = useCallback((type: ToastType) => {
    const iconProps = {
      sx: {
        fontSize: size === TOAST_SIZES.COMPACT ? 20 : size === TOAST_SIZES.EXPANDED ? 28 : 24,
        color: getTypeColor(type),
      }
    };

    switch (type) {
      case 'success':
        return <CheckCircleIcon {...iconProps} />;
      case 'error':
        return <ErrorIcon {...iconProps} />;
      case 'warning':
        return <WarningIcon {...iconProps} />;
      case 'info':
      default:
        return <InfoIcon {...iconProps} />;
    }
  }, [size, getTypeColor]);

  const getSizeStyles = useCallback(() => {
    switch (size) {
      case TOAST_SIZES.COMPACT:
        return {
          minHeight: 48,
          padding: theme.spacing(1, 2),
          fontSize: '0.875rem'
        };
      case TOAST_SIZES.EXPANDED:
        return {
          minHeight: 80,
          padding: theme.spacing(3),
          fontSize: '1rem'
        };
      case TOAST_SIZES.STANDARD:
      default:
        return {
          minHeight: 64,
          padding: theme.spacing(2),
          fontSize: '0.9375rem'
        };
    }
  }, [size, theme]);

  // Enhanced effects for component lifecycle
  useEffect(() => {
    if (!isRead) {
      setIsRead(true);

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack('toast_viewed', {
          toastId: toast.id,
          type: toast.config.type,
          timestamp: new Date().toISOString()
        });
      }
    }
  }, [isRead, enableAnalytics, onAnalyticsTrack, toast.id, toast.config.type]);

  // Progress tracking for auto-dismiss
  useEffect(() => {
    if (showProgress && toast.config.duration && toast.config.duration > 0) {
      const interval = setInterval(() => {
        setProgressValue(prev => {
          const increment = 100 / (toast.config.duration! / 100);
          return Math.min(prev + increment, 100);
        });
      }, 100);

      return () => clearInterval(interval);
    }
  }, [showProgress, toast.config.duration]);

  // Analytics tracking for view time
  useEffect(() => {
    const startTime = Date.now();

    return () => {
      const viewTime = Date.now() - startTime;
      setToastAnalytics(prev => ({
        ...prev,
        viewTime: prev.viewTime + viewTime
      }));
    };
  }, []);

  // Get color scheme based on toast type
  const getColorScheme = (type: ToastType) => {
    switch (type) {
      case 'success':
        return {
          main: theme.palette.success.main,
          light: theme.palette.success.light,
          dark: theme.palette.success.dark,
          bg: alpha(theme.palette.success.main, 0.1),
          border: theme.palette.success.main,
        };
      case 'error':
        return {
          main: theme.palette.error.main,
          light: theme.palette.error.light,
          dark: theme.palette.error.dark,
          bg: alpha(theme.palette.error.main, 0.1),
          border: theme.palette.error.main,
        };
      case 'warning':
        return {
          main: theme.palette.warning.main,
          light: theme.palette.warning.light,
          dark: theme.palette.warning.dark,
          bg: alpha(theme.palette.warning.main, 0.1),
          border: theme.palette.warning.main,
        };
      case 'info':
      default:
        return {
          main: theme.palette.info.main,
          light: theme.palette.info.light,
          dark: theme.palette.info.dark,
          bg: alpha(theme.palette.info.main, 0.1),
          border: theme.palette.info.main,
        };
    }
  };

  const colorScheme = getColorScheme(toast.config.type);

  // Handle retry action
  const handleRetry = useCallback(() => {
    if (toast.config.maxRetries && retryCount >= toast.config.maxRetries) {
      return;
    }

    setRetryCount(prev => prev + 1);

    if (toast.config.onAction) {
      toast.config.onAction();
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack('toast_retry_attempted', {
        toastId: toast.id,
        retryCount: retryCount + 1,
        timestamp: new Date().toISOString()
      });
    }

    announceToScreenReader(`Retry attempt ${retryCount + 1}`);
  }, [
    toast.config,
    retryCount,
    enableAnalytics,
    onAnalyticsTrack,
    toast.id,
    announceToScreenReader
  ]);

  // Focus management for accessibility
  useEffect(() => {
    if (toast.config.type === 'error' && toastRef.current) {
      // Auto-focus error toasts for screen readers
      toastRef.current.focus();
    }
  }, [toast.config.type]);

  // Announce to screen readers
  useEffect(() => {
    const announcement = `${toast.config.type} notification: ${toast.config.title || ''} ${toast.config.message}`;
    
    // Create a temporary element for screen reader announcement
    const announcer = document.createElement('div');
    announcer.setAttribute('aria-live', toast.config.type === 'error' ? 'assertive' : 'polite');
    announcer.setAttribute('aria-atomic', 'true');
    announcer.className = 'sr-only';
    announcer.textContent = announcement;
    
    document.body.appendChild(announcer);
    
    // Clean up after announcement
    setTimeout(() => {
      document.body.removeChild(announcer);
    }, 1000);
  }, [toast.config.type, toast.config.title, toast.config.message]);

  // Enhanced render methods
  const renderActions = useCallback(() => {
    const actions = [];

    // Add retry button for failed operations
    if (toast.config.type === 'error' && toast.config.maxRetries && retryCount < toast.config.maxRetries) {
      actions.push(
        <Button
          key="retry"
          size="small"
          startIcon={<RefreshIcon />}
          onClick={handleRetry}
          sx={{
            minWidth: 'auto',
            color: getTypeColor(toast.config.type),
            '&:hover': {
              backgroundColor: alpha(getTypeColor(toast.config.type), 0.1),
            },
          }}
        >
          Retry ({toast.config.maxRetries - retryCount} left)
        </Button>
      );
    }

    // Add undo button
    if (toast.config.onUndo) {
      actions.push(
        <Button
          key="undo"
          size="small"
          startIcon={<UndoIcon />}
          onClick={() => onUndo?.(toast.id)}
          sx={{
            minWidth: 'auto',
            color: getTypeColor(toast.config.type),
            '&:hover': {
              backgroundColor: alpha(getTypeColor(toast.config.type), 0.1),
            },
          }}
        >
          Undo
        </Button>
      );
    }

    // Add custom action buttons
    if (toast.config.actions) {
      toast.config.actions.forEach((action, index) => {
        actions.push(
          <Button
            key={`action-${index}`}
            size="small"
            variant={action.variant || 'text'}
            color={action.color || 'primary'}
            onClick={() => {
              action.onClick();
              handleActionClick(index);
            }}
            sx={{ minWidth: 'auto' }}
          >
            {action.label}
          </Button>
        );
      });
    }

    return actions.length > 0 ? (
      <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
        {actions}
      </Stack>
    ) : null;
  }, [
    toast.config.type,
    toast.config.maxRetries,
    toast.config.onUndo,
    toast.config.actions,
    retryCount,
    handleRetry,
    onUndo,
    toast.id,
    handleActionClick,
    getTypeColor
  ]);

  const renderExpandableContent = useCallback(() => {
    if (!enableExpandable) return null;

    return (
      <Collapse in={isExpanded}>
        <Box sx={{ mt: 2, pt: 2, borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}` }}>
          <Typography variant="body2" sx={{ color: theme.palette.text.secondary }}>
            Additional content would be displayed here when expanded.
          </Typography>
        </Box>
      </Collapse>
    );
  }, [enableExpandable, isExpanded, theme]);

  const renderProgressIndicator = useCallback(() => {
    if (!showProgress) return null;

    return (
      <LinearProgress
        ref={progressRef}
        variant="determinate"
        value={progressValue}
        sx={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          height: 3,
          borderRadius: '0 0 8px 8px',
          backgroundColor: alpha(getTypeColor(toast.config.type), 0.2),
          '& .MuiLinearProgress-bar': {
            backgroundColor: getTypeColor(toast.config.type)
          }
        }}
      />
    );
  }, [showProgress, progressValue, getTypeColor, toast.config.type]);

  // Enhanced keyboard navigation
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (!enableKeyboardNavigation) return;

    switch (event.key) {
      case 'Escape':
        event.preventDefault();
        handleDismiss();
        break;
      case 'Enter':
      case ' ':
        if (toast.config.actions && toast.config.actions.length > 0) {
          event.preventDefault();
          handleActionClick(0);
        }
        break;
      case 'u':
      case 'U':
        if (event.ctrlKey && toast.config.onUndo) {
          event.preventDefault();
          onUndo?.(toast.id);
        }
        break;
      case 'e':
      case 'E':
        if (enableExpandable) {
          event.preventDefault();
          setIsExpanded(!isExpanded);
        }
        break;
    }
  }, [
    enableKeyboardNavigation,
    handleDismiss,
    toast.config.actions,
    toast.config.onUndo,
    toast.id,
    handleActionClick,
    onUndo,
    enableExpandable,
    isExpanded
  ]);

  // Calculate position based on stack index
  const getStackOffset = useCallback(() => {
    const baseOffset = stackIndex * 8; // 8px spacing between stacked toasts
    const scaleOffset = stackIndex * 0.02; // Slight scale reduction for depth
    return {
      transform: `translateY(-${baseOffset}px) scale(${1 - scaleOffset})`,
      zIndex: toast.zIndex - stackIndex,
    };
  }, [stackIndex, toast.zIndex]);

  // Get animation component
  const getAnimationComponent = useCallback(() => {
    switch (animationType) {
      case ANIMATION_TYPES.FADE:
        return Fade;
      case ANIMATION_TYPES.ZOOM:
        return Zoom;
      case ANIMATION_TYPES.SLIDE:
      default:
        return Slide;
    }
  }, [animationType]);

  // Render priority indicator
  const renderPriorityIndicator = () => {
    if (!toast.config.priority || toast.config.priority === 'normal') return null;

    const priorityColors = {
      low: theme.palette.grey[500],
      high: theme.palette.warning.main,
      critical: theme.palette.error.main,
    };

    return (
      <Chip
        label={toast.config.priority.toUpperCase()}
        size="small"
        sx={{
          height: 20,
          fontSize: '0.7rem',
          backgroundColor: alpha(priorityColors[toast.config.priority], 0.2),
          color: priorityColors[toast.config.priority],
          fontWeight: 'bold',
        }}
      />
    );
  };

  // Enhanced interaction handlers
  const handleInteractionStateChange = useCallback((newState: string) => {
    setInteractionState(newState as keyof typeof INTERACTION_STATES);

    if (onInteraction) {
      onInteraction(toast.id, newState);
    }
  }, [onInteraction, toast.id]);

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    dismiss: () => handleDismiss(),
    expand: () => setIsExpanded(true),
    collapse: () => setIsExpanded(false),
    focus: () => toastRef.current?.focus(),
    getInteractionState: () => interactionState,
    triggerAction: (actionIndex: number) => handleActionClick(actionIndex)
  }), [interactionState, handleDismiss, handleActionClick]);

  const TransitionComponent = getAnimationComponent();
  const sizeStyles = getSizeStyles();

  // Animation props based on component type
  const animationProps = useMemo(() => {
    const baseProps = {
      in: toast.isVisible && !toast.isExiting,
      timeout: animationDuration,
      unmountOnExit: true
    };

    if (animationType === ANIMATION_TYPES.SLIDE) {
      return {
        ...baseProps,
        direction: (position.includes('right') ? 'left' : 'right') as 'left' | 'right'
      };
    }

    return baseProps;
  }, [toast.isVisible, toast.isExiting, animationDuration, animationType, position]);

  return (
    <TransitionComponent {...animationProps}>
      <Paper
        ref={toastRef}
        elevation={interactionState === INTERACTION_STATES.HOVERED || interactionState === INTERACTION_STATES.FOCUSED ? 8 : 4}
        tabIndex={enableKeyboardNavigation ? 0 : -1}
        role="alert"
        aria-live={toast.config.type === 'error' ? 'assertive' : 'polite'}
        aria-atomic="true"
        aria-expanded={enableExpandable ? isExpanded : undefined}
        onMouseEnter={() => handleInteractionStateChange(INTERACTION_STATES.HOVERED)}
        onMouseLeave={() => handleInteractionStateChange(INTERACTION_STATES.IDLE)}
        onFocus={() => handleInteractionStateChange(INTERACTION_STATES.FOCUSED)}
        onBlur={() => handleInteractionStateChange(INTERACTION_STATES.IDLE)}
        onKeyDown={handleKeyDown}
        sx={{
          position: 'relative',
          display: 'flex',
          alignItems: 'flex-start',
          borderLeft: `4px solid ${getTypeColor(toast.config.type)}`,
          width: '100%',
          maxWidth: { xs: 'calc(100vw - 32px)', sm: 400 },
          minWidth: { xs: 280, sm: 320 },
          cursor: 'pointer',
          '&:hover': {
            transform: `translateY(-2px) translateX(${swipeOffset}px)`,
            boxShadow: theme.shadows[8],
          },
          '&:focus-visible': {
            outline: `3px solid ${getTypeColor(toast.config.type)}`,
            outlineOffset: '2px',
          },
          // High contrast mode support
          '@media (prefers-contrast: high)': {
            border: `2px solid ${getTypeColor(toast.config.type)}`,
            backgroundColor: theme.palette.background.paper,
          },
          // Reduced motion support
          '@media (prefers-reduced-motion: reduce)': {
            transition: 'none',
            '&:hover': {
              transform: 'none',
            },
          },
          ...glassMorphismStyles,
          ...sizeStyles,
          ...getStackOffset(),
          transform: `translateX(${swipeOffset}px)`,
        }}
      >
        {/* Progress indicator */}
        {renderProgressIndicator()}

        {/* Read status indicator */}
        {!isRead && (
          <Badge
            color="primary"
            variant="dot"
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              '& .MuiBadge-dot': {
                backgroundColor: ACE_COLORS.PURPLE
              }
            }}
          />
        )}

        {/* Icon */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: getTypeColor(toast.config.type),
            mr: 1.5,
            mt: 0.5,
          }}
        >
          {getIcon(toast.config.type)}
        </Box>

        {/* Content */}
        <Box sx={{ flexGrow: 1, minWidth: 0 }}>
          {/* Header with title and priority */}
          {(toast.config.title || toast.config.priority) && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
              {toast.config.title && (
                <Typography
                  variant="subtitle2"
                  sx={{
                    fontWeight: 600,
                    color: theme.palette.text.primary,
                    flexGrow: 1,
                  }}
                >
                  {toast.config.title}
                </Typography>
              )}
              {renderPriorityIndicator()}
            </Box>
          )}

          {/* Message */}
          <Typography
            variant="body2"
            sx={{
              color: theme.palette.text.secondary,
              wordBreak: 'break-word',
              lineHeight: 1.4,
            }}
          >
            {toast.config.message}
          </Typography>

          {/* Actions */}
          {renderActions()}

          {/* Expandable content */}
          {renderExpandableContent()}

          {/* Metadata */}
          {toast.config.metadata && (
            <Typography
              variant="caption"
              sx={{
                display: 'block',
                mt: 1,
                color: theme.palette.text.disabled,
                fontSize: '0.7rem',
              }}
            >
              {new Date(toast.createdAt).toLocaleTimeString()}
            </Typography>
          )}
        </Box>

        {/* Expand/Collapse button */}
        {enableExpandable && (
          <IconButton
            size="small"
            aria-label={isExpanded ? 'Collapse notification' : 'Expand notification'}
            onClick={() => setIsExpanded(!isExpanded)}
            sx={{
              ml: 1,
              color: theme.palette.text.secondary,
              '&:hover': {
                backgroundColor: alpha(getTypeColor(toast.config.type), 0.1),
                color: getTypeColor(toast.config.type),
              },
            }}
          >
            {isExpanded ? <ExpandLessIcon fontSize="small" /> : <ExpandMoreIcon fontSize="small" />}
          </IconButton>
        )}

        {/* Close button */}
        {toast.config.dismissible !== false && (
          <IconButton
            size="small"
            aria-label="Close notification"
            onClick={(e) => {
              e.stopPropagation();
              handleDismiss();
            }}
            sx={{
              ml: 1,
              color: theme.palette.text.secondary,
              '&:hover': {
                backgroundColor: alpha(getTypeColor(toast.config.type), 0.1),
                color: getTypeColor(toast.config.type),
              },
            }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        )}
      </Paper>
    </TransitionComponent>
  );
}));

ProductionToast.displayName = 'ProductionToast';

export default ProductionToast;
