/**
 * Production-Ready Toast Component
 * Modern, accessible, and responsive toast notification component
 */

import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Button,
  Slide,
  Fade,
  Grow,
  useTheme,
  alpha,
  Stack,
  Chip,
} from '@mui/material';
import {
  Close as CloseIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Undo as UndoIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { ToastState, ToastType } from '../../types/toast';

interface ProductionToastProps {
  toast: ToastState;
  onDismiss: (id: string) => void;
  onAction?: (id: string) => void;
  onUndo?: (id: string) => void;
  position: 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right';
  stackIndex: number;
  animationDuration?: number;
  enableKeyboardNavigation?: boolean;
}

const ProductionToast: React.FC<ProductionToastProps> = ({
  toast,
  onDismiss,
  onAction,
  onUndo,
  position,
  stackIndex,
  animationDuration = 300,
  enableKeyboardNavigation = true,
}) => {
  const theme = useTheme();
  const toastRef = useRef<HTMLDivElement>(null);
  const [isHovered, setIsHovered] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [retryCount, setRetryCount] = useState(toast.config.retryCount || 0);

  // Get icon based on toast type
  const getIcon = (type: ToastType) => {
    const iconProps = { fontSize: 'small' as const };
    switch (type) {
      case 'success':
        return <CheckCircleIcon {...iconProps} />;
      case 'error':
        return <ErrorIcon {...iconProps} />;
      case 'warning':
        return <WarningIcon {...iconProps} />;
      case 'info':
      default:
        return <InfoIcon {...iconProps} />;
    }
  };

  // Get color scheme based on toast type
  const getColorScheme = (type: ToastType) => {
    switch (type) {
      case 'success':
        return {
          main: theme.palette.success.main,
          light: theme.palette.success.light,
          dark: theme.palette.success.dark,
          bg: alpha(theme.palette.success.main, 0.1),
          border: theme.palette.success.main,
        };
      case 'error':
        return {
          main: theme.palette.error.main,
          light: theme.palette.error.light,
          dark: theme.palette.error.dark,
          bg: alpha(theme.palette.error.main, 0.1),
          border: theme.palette.error.main,
        };
      case 'warning':
        return {
          main: theme.palette.warning.main,
          light: theme.palette.warning.light,
          dark: theme.palette.warning.dark,
          bg: alpha(theme.palette.warning.main, 0.1),
          border: theme.palette.warning.main,
        };
      case 'info':
      default:
        return {
          main: theme.palette.info.main,
          light: theme.palette.info.light,
          dark: theme.palette.info.dark,
          bg: alpha(theme.palette.info.main, 0.1),
          border: theme.palette.info.main,
        };
    }
  };

  const colorScheme = getColorScheme(toast.config.type);

  // Calculate position based on stack index
  const getStackOffset = () => {
    const baseOffset = stackIndex * 8; // 8px spacing between stacked toasts
    const scaleOffset = stackIndex * 0.02; // Slight scale reduction for depth
    return {
      transform: `translateY(-${baseOffset}px) scale(${1 - scaleOffset})`,
      zIndex: toast.zIndex - stackIndex,
    };
  };

  // Handle keyboard navigation
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (!enableKeyboardNavigation) return;

    switch (event.key) {
      case 'Escape':
        event.preventDefault();
        onDismiss(toast.id);
        break;
      case 'Enter':
      case ' ':
        if (toast.config.actions && toast.config.actions.length > 0) {
          event.preventDefault();
          onAction?.(toast.id);
        }
        break;
      case 'u':
      case 'U':
        if (event.ctrlKey && toast.config.onUndo) {
          event.preventDefault();
          onUndo?.(toast.id);
        }
        break;
    }
  }, [enableKeyboardNavigation, toast.id, toast.config.actions, toast.config.onUndo, onDismiss, onAction, onUndo]);

  // Handle retry action
  const handleRetry = useCallback(() => {
    if (toast.config.maxRetries && retryCount >= toast.config.maxRetries) {
      return;
    }
    
    setRetryCount(prev => prev + 1);
    // Trigger retry logic here
    if (toast.config.onAction) {
      toast.config.onAction();
    }
  }, [toast.config, retryCount]);

  // Focus management for accessibility
  useEffect(() => {
    if (toast.config.type === 'error' && toastRef.current) {
      // Auto-focus error toasts for screen readers
      toastRef.current.focus();
    }
  }, [toast.config.type]);

  // Announce to screen readers
  useEffect(() => {
    const announcement = `${toast.config.type} notification: ${toast.config.title || ''} ${toast.config.message}`;
    
    // Create a temporary element for screen reader announcement
    const announcer = document.createElement('div');
    announcer.setAttribute('aria-live', toast.config.type === 'error' ? 'assertive' : 'polite');
    announcer.setAttribute('aria-atomic', 'true');
    announcer.className = 'sr-only';
    announcer.textContent = announcement;
    
    document.body.appendChild(announcer);
    
    // Clean up after announcement
    setTimeout(() => {
      document.body.removeChild(announcer);
    }, 1000);
  }, [toast.config.type, toast.config.title, toast.config.message]);

  // Render action buttons
  const renderActions = () => {
    const actions = [];

    // Add retry button for failed operations
    if (toast.config.type === 'error' && toast.config.maxRetries && retryCount < toast.config.maxRetries) {
      actions.push(
        <Button
          key="retry"
          size="small"
          startIcon={<RefreshIcon />}
          onClick={handleRetry}
          sx={{
            minWidth: 'auto',
            color: colorScheme.main,
            '&:hover': {
              backgroundColor: alpha(colorScheme.main, 0.1),
            },
          }}
        >
          Retry ({toast.config.maxRetries - retryCount} left)
        </Button>
      );
    }

    // Add undo button
    if (toast.config.onUndo) {
      actions.push(
        <Button
          key="undo"
          size="small"
          startIcon={<UndoIcon />}
          onClick={() => onUndo?.(toast.id)}
          sx={{
            minWidth: 'auto',
            color: colorScheme.main,
            '&:hover': {
              backgroundColor: alpha(colorScheme.main, 0.1),
            },
          }}
        >
          Undo
        </Button>
      );
    }

    // Add custom action buttons
    if (toast.config.actions) {
      toast.config.actions.forEach((action, index) => {
        actions.push(
          <Button
            key={`action-${index}`}
            size="small"
            variant={action.variant || 'text'}
            color={action.color || 'primary'}
            onClick={() => {
              action.onClick();
              onAction?.(toast.id);
            }}
            sx={{ minWidth: 'auto' }}
          >
            {action.label}
          </Button>
        );
      });
    }

    return actions.length > 0 ? (
      <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
        {actions}
      </Stack>
    ) : null;
  };

  // Render priority indicator
  const renderPriorityIndicator = () => {
    if (!toast.config.priority || toast.config.priority === 'normal') return null;

    const priorityColors = {
      low: theme.palette.grey[500],
      high: theme.palette.warning.main,
      critical: theme.palette.error.main,
    };

    return (
      <Chip
        label={toast.config.priority.toUpperCase()}
        size="small"
        sx={{
          height: 20,
          fontSize: '0.7rem',
          backgroundColor: alpha(priorityColors[toast.config.priority], 0.2),
          color: priorityColors[toast.config.priority],
          fontWeight: 'bold',
        }}
      />
    );
  };

  const TransitionComponent = toast.isExiting ? Fade : Slide;

  return (
    <TransitionComponent
      in={toast.isVisible && !toast.isExiting}
      direction={position.includes('right') ? 'left' : 'right'}
      timeout={animationDuration}
      unmountOnExit
    >
      <Paper
        ref={toastRef}
        elevation={isHovered || isFocused ? 8 : 4}
        tabIndex={enableKeyboardNavigation ? 0 : -1}
        role="alert"
        aria-live={toast.config.type === 'error' ? 'assertive' : 'polite'}
        aria-atomic="true"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        onKeyDown={handleKeyDown}
        sx={{
          display: 'flex',
          alignItems: 'flex-start',
          p: 2,
          borderRadius: 2,
          borderLeft: '4px solid',
          borderLeftColor: colorScheme.border,
          backgroundColor: theme.palette.mode === 'dark' 
            ? alpha(colorScheme.main, 0.15) 
            : colorScheme.bg,
          backdropFilter: 'blur(8px)',
          width: '100%',
          maxWidth: { xs: 'calc(100vw - 32px)', sm: 400 },
          minWidth: { xs: 280, sm: 320 },
          cursor: 'pointer',
          transition: theme.transitions.create([
            'transform',
            'box-shadow',
            'background-color',
          ], {
            duration: animationDuration,
            easing: theme.transitions.easing.easeInOut,
          }),
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: theme.shadows[8],
          },
          '&:focus-visible': {
            outline: `3px solid ${colorScheme.main}`,
            outlineOffset: '2px',
          },
          // High contrast mode support
          '@media (prefers-contrast: high)': {
            border: `2px solid ${colorScheme.main}`,
            backgroundColor: theme.palette.background.paper,
          },
          // Reduced motion support
          '@media (prefers-reduced-motion: reduce)': {
            transition: 'none',
            '&:hover': {
              transform: 'none',
            },
          },
          ...getStackOffset(),
        }}
      >
        {/* Icon */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: colorScheme.main,
            mr: 1.5,
            mt: 0.5,
          }}
        >
          {getIcon(toast.config.type)}
        </Box>

        {/* Content */}
        <Box sx={{ flexGrow: 1, minWidth: 0 }}>
          {/* Header with title and priority */}
          {(toast.config.title || toast.config.priority) && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
              {toast.config.title && (
                <Typography
                  variant="subtitle2"
                  sx={{
                    fontWeight: 600,
                    color: theme.palette.text.primary,
                    flexGrow: 1,
                  }}
                >
                  {toast.config.title}
                </Typography>
              )}
              {renderPriorityIndicator()}
            </Box>
          )}

          {/* Message */}
          <Typography
            variant="body2"
            sx={{
              color: theme.palette.text.secondary,
              wordBreak: 'break-word',
              lineHeight: 1.4,
            }}
          >
            {toast.config.message}
          </Typography>

          {/* Actions */}
          {renderActions()}

          {/* Metadata */}
          {toast.config.metadata && (
            <Typography
              variant="caption"
              sx={{
                display: 'block',
                mt: 1,
                color: theme.palette.text.disabled,
                fontSize: '0.7rem',
              }}
            >
              {new Date(toast.createdAt).toLocaleTimeString()}
            </Typography>
          )}
        </Box>

        {/* Close button */}
        {toast.config.dismissible !== false && (
          <IconButton
            size="small"
            aria-label="Close notification"
            onClick={(e) => {
              e.stopPropagation();
              onDismiss(toast.id);
            }}
            sx={{
              ml: 1,
              color: theme.palette.text.secondary,
              '&:hover': {
                backgroundColor: alpha(colorScheme.main, 0.1),
                color: colorScheme.main,
              },
            }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        )}
      </Paper>
    </TransitionComponent>
  );
};

export default ProductionToast;
