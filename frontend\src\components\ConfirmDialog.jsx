/**
 * Enhanced Confirm Dialog - Enterprise-grade confirmation dialog component
 * Features: Comprehensive confirmation dialog with advanced confirmation handling, customizable
 * messaging, and action validation, detailed dialog customization with dynamic content and
 * personalized confirmation flows, advanced confirmation features with timeout handling and
 * keyboard shortcuts, ACE Social's notification system integration with seamless dialog lifecycle
 * management, confirmation interaction features including dismissible dialogs and auto-dismiss
 * timers, confirmation state management with real-time updates and validation checks, real-time
 * confirmation updates with live validation displays and dynamic messaging, and seamless ACE
 * Social platform integration with advanced confirmation orchestration and comprehensive
 * accessibility compliance
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 */

import {
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef
} from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  CircularProgress,
  Typography,
  Box,
  IconButton,
  Tooltip,
  LinearProgress,
  Chip,
  Stack,
  Alert,
  Collapse,
  alpha,
  useTheme
} from '@mui/material';
import {
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Security as SecurityIcon,
  Keyboard as KeyboardIcon
} from '@mui/icons-material';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Dialog types
const DIALOG_TYPES = {
  INFO: 'info',
  WARNING: 'warning',
  ERROR: 'error',
  SUCCESS: 'success',
  SECURITY: 'security'
};

// Confirmation levels
const CONFIRMATION_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};
/**
 * Enhanced Confirm Dialog - Comprehensive confirmation dialog with advanced features
 * Implements detailed confirmation handling and enterprise-grade user interaction capabilities
 */
const ConfirmDialog = memo(forwardRef(({
  open,
  title,
  content,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  onConfirm,
  onCancel,
  loading = false,
  confirmColor = 'primary',
  dialogType = DIALOG_TYPES.INFO,
  confirmationLevel = CONFIRMATION_LEVELS.MEDIUM,
  enableAccessibility = true,
  enableAnalytics = true,
  enableKeyboardShortcuts = true,
  enableAutoTimeout = false,
  timeoutSeconds = 30,
  showProgress = false,
  requireDoubleConfirm = false,
  showKeyboardHints = true,
  customIcon = null,
  onTimeout,
  onAnalyticsTrack,
  onDialogInteraction
}, ref) => {
  const theme = useTheme();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Core refs for component management
  const dialogRef = useRef(null);
  const confirmButtonRef = useRef(null);

  // Enhanced state management
  const [isDoubleConfirmStep, setIsDoubleConfirmStep] = useState(false);
  const [timeoutProgress, setTimeoutProgress] = useState(100);
  const [showKeyboardHelp, setShowKeyboardHelp] = useState(false);
  const [dialogAnalytics, setDialogAnalytics] = useState({
    openTime: null,
    interactions: 0,
    keyboardUsed: false,
    lastActivity: new Date().toISOString()
  });

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    focus: () => confirmButtonRef.current?.focus(),
    close: () => handleCancel(),
    confirm: () => handleConfirm(),
    showKeyboardHelp: () => setShowKeyboardHelp(true),
    getAnalytics: () => dialogAnalytics,
    resetAnalytics: () => setDialogAnalytics({
      openTime: null,
      interactions: 0,
      keyboardUsed: false,
      lastActivity: new Date().toISOString()
    })
  }), [dialogAnalytics, handleCancel, handleConfirm]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced dialog type configuration
  const dialogConfig = useMemo(() => {
    const configs = {
      [DIALOG_TYPES.INFO]: {
        icon: <InfoIcon />,
        color: ACE_COLORS.PURPLE,
        severity: 'info'
      },
      [DIALOG_TYPES.WARNING]: {
        icon: <WarningIcon />,
        color: ACE_COLORS.YELLOW,
        severity: 'warning'
      },
      [DIALOG_TYPES.ERROR]: {
        icon: <ErrorIcon />,
        color: '#F44336',
        severity: 'error'
      },
      [DIALOG_TYPES.SUCCESS]: {
        icon: <CheckCircleIcon />,
        color: '#4CAF50',
        severity: 'success'
      },
      [DIALOG_TYPES.SECURITY]: {
        icon: <SecurityIcon />,
        color: '#FF5722',
        severity: 'error'
      }
    };
    return configs[dialogType] || configs[DIALOG_TYPES.INFO];
  }, [dialogType]);

  // Enhanced handlers
  const handleConfirm = useCallback(() => {
    if (requireDoubleConfirm && !isDoubleConfirmStep) {
      setIsDoubleConfirmStep(true);
      setDialogAnalytics(prev => ({
        ...prev,
        interactions: prev.interactions + 1,
        lastActivity: new Date().toISOString()
      }));

      if (enableAccessibility) {
        announceToScreenReader('Double confirmation required. Click confirm again to proceed.');
      }

      if (onDialogInteraction) {
        onDialogInteraction('double_confirm_step', {
          timestamp: new Date().toISOString()
        });
      }

      return;
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack('dialog_confirmed', {
        dialogType,
        confirmationLevel,
        doubleConfirm: requireDoubleConfirm,
        timeToConfirm: dialogAnalytics.openTime ? Date.now() - dialogAnalytics.openTime : 0,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader('Action confirmed');
    }

    onConfirm();
  }, [
    requireDoubleConfirm,
    isDoubleConfirmStep,
    enableAccessibility,
    announceToScreenReader,
    onDialogInteraction,
    enableAnalytics,
    onAnalyticsTrack,
    dialogType,
    confirmationLevel,
    dialogAnalytics.openTime,
    onConfirm
  ]);

  const handleCancel = useCallback(() => {
    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack('dialog_cancelled', {
        dialogType,
        timeToCancel: dialogAnalytics.openTime ? Date.now() - dialogAnalytics.openTime : 0,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader('Action cancelled');
    }

    setIsDoubleConfirmStep(false);
    onCancel();
  }, [
    enableAnalytics,
    onAnalyticsTrack,
    dialogType,
    dialogAnalytics.openTime,
    enableAccessibility,
    announceToScreenReader,
    onCancel
  ]);

  // Enhanced effects for component lifecycle
  useEffect(() => {
    if (open) {
      setDialogAnalytics(prev => ({
        ...prev,
        openTime: Date.now(),
        interactions: 0,
        keyboardUsed: false,
        lastActivity: new Date().toISOString()
      }));

      if (enableAccessibility) {
        announceToScreenReader(`Confirmation dialog opened: ${title || 'Confirm action'}`);
      }

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack('dialog_opened', {
          dialogType,
          confirmationLevel,
          timestamp: new Date().toISOString()
        });
      }
    } else {
      setIsDoubleConfirmStep(false);
      setTimeoutProgress(100);
    }
  }, [
    open,
    title,
    enableAccessibility,
    announceToScreenReader,
    enableAnalytics,
    onAnalyticsTrack,
    dialogType,
    confirmationLevel
  ]);

  // Enhanced keyboard shortcuts
  useEffect(() => {
    if (!open || !enableKeyboardShortcuts) return;

    const handleKeyDown = (event) => {
      setDialogAnalytics(prev => ({
        ...prev,
        keyboardUsed: true,
        interactions: prev.interactions + 1,
        lastActivity: new Date().toISOString()
      }));

      switch (event.key) {
        case 'Enter':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            handleConfirm();
          }
          break;
        case 'Escape':
          if (!loading) {
            event.preventDefault();
            handleCancel();
          }
          break;
        case 'F1':
          event.preventDefault();
          setShowKeyboardHelp(!showKeyboardHelp);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [
    open,
    enableKeyboardShortcuts,
    loading,
    showKeyboardHelp,
    handleConfirm,
    handleCancel
  ]);

  // Enhanced auto-timeout functionality
  useEffect(() => {
    if (!open || !enableAutoTimeout || timeoutSeconds <= 0) return;

    const interval = setInterval(() => {
      setTimeoutProgress(prev => {
        const newProgress = prev - (100 / timeoutSeconds);
        if (newProgress <= 0) {
          if (onTimeout) {
            onTimeout();
          } else {
            handleCancel();
          }
          return 0;
        }
        return newProgress;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [open, enableAutoTimeout, timeoutSeconds, onTimeout, handleCancel]);

  return (
    <Dialog
      ref={dialogRef}
      open={open}
      onClose={loading ? undefined : handleCancel}
      maxWidth="sm"
      fullWidth
      aria-labelledby="confirm-dialog-title"
      aria-describedby="confirm-dialog-description"
      PaperProps={{
        sx: {
          ...glassMorphismStyles,
          position: 'relative'
        }
      }}
    >
      {/* Timeout Progress Bar */}
      {enableAutoTimeout && timeoutSeconds > 0 && (
        <LinearProgress
          variant="determinate"
          value={timeoutProgress}
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: 4,
            backgroundColor: alpha(dialogConfig.color, 0.2),
            '& .MuiLinearProgress-bar': {
              backgroundColor: dialogConfig.color
            }
          }}
        />
      )}

      {/* Dialog Title with Icon */}
      {title && (
        <DialogTitle id="confirm-dialog-title">
          <Stack direction="row" alignItems="center" spacing={2}>
            {customIcon || dialogConfig.icon}
            <Typography variant="h6" component="span">
              {title}
            </Typography>
            {confirmationLevel === CONFIRMATION_LEVELS.CRITICAL && (
              <Chip
                label="Critical"
                size="small"
                color="error"
                variant="outlined"
              />
            )}
          </Stack>
        </DialogTitle>
      )}

      {/* Dialog Content */}
      <DialogContent id="confirm-dialog-description">
        {/* Double Confirmation Alert */}
        {requireDoubleConfirm && isDoubleConfirmStep && (
          <Alert severity="warning" sx={{ mb: 2 }}>
            <Typography variant="body2">
              <strong>Double confirmation required:</strong> Click confirm again to proceed with this action.
            </Typography>
          </Alert>
        )}

        {/* Main Content */}
        <Box>
          {typeof content === 'string' ? (
            <Typography variant="body1">{content}</Typography>
          ) : (
            content
          )}
        </Box>

        {/* Keyboard Help */}
        <Collapse in={showKeyboardHelp && showKeyboardHints}>
          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2" gutterBottom>
              <strong>Keyboard Shortcuts:</strong>
            </Typography>
            <Typography variant="caption" component="div">
              • Ctrl+Enter: Confirm action
            </Typography>
            <Typography variant="caption" component="div">
              • Escape: Cancel action
            </Typography>
            <Typography variant="caption" component="div">
              • F1: Toggle this help
            </Typography>
          </Alert>
        </Collapse>

        {/* Progress Indicator */}
        {showProgress && loading && (
          <Box sx={{ mt: 2, textAlign: 'center' }}>
            <CircularProgress size={24} />
            <Typography variant="caption" display="block" sx={{ mt: 1 }}>
              Processing...
            </Typography>
          </Box>
        )}
      </DialogContent>

      {/* Dialog Actions */}
      <DialogActions sx={{ p: 3, gap: 1 }}>
        {/* Keyboard Help Toggle */}
        {showKeyboardHints && enableKeyboardShortcuts && (
          <Tooltip title="Show keyboard shortcuts (F1)">
            <IconButton
              size="small"
              onClick={() => setShowKeyboardHelp(!showKeyboardHelp)}
              sx={{ mr: 'auto' }}
            >
              <KeyboardIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}

        {/* Cancel Button */}
        <Button
          onClick={handleCancel}
          disabled={loading}
          color="inherit"
          variant="outlined"
        >
          {cancelText}
        </Button>

        {/* Confirm Button */}
        <Button
          ref={confirmButtonRef}
          onClick={handleConfirm}
          color={confirmColor}
          variant="contained"
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : null}
          sx={{
            backgroundColor: isDoubleConfirmStep ? dialogConfig.color : undefined,
            '&:hover': {
              backgroundColor: isDoubleConfirmStep ? alpha(dialogConfig.color, 0.8) : undefined
            }
          }}
        >
          {isDoubleConfirmStep ? 'Confirm Again' : confirmText}
        </Button>
      </DialogActions>
    </Dialog>
  );
}));

ConfirmDialog.displayName = 'ConfirmDialog';

ConfirmDialog.propTypes = {
  /** Whether the dialog is open */
  open: PropTypes.bool.isRequired,
  /** Dialog title */
  title: PropTypes.string,
  /** Dialog content */
  content: PropTypes.node.isRequired,
  /** Text for the confirm button */
  confirmText: PropTypes.string,
  /** Text for the cancel button */
  cancelText: PropTypes.string,
  /** Function to call when confirm button is clicked */
  onConfirm: PropTypes.func.isRequired,
  /** Function to call when cancel button is clicked */
  onCancel: PropTypes.func.isRequired,
  /** Whether the confirm action is loading */
  loading: PropTypes.bool,
  /** Color of the confirm button */
  confirmColor: PropTypes.string,
  /** Dialog type for styling and icons */
  dialogType: PropTypes.oneOf(Object.values(DIALOG_TYPES)),
  /** Confirmation level for security */
  confirmationLevel: PropTypes.oneOf(Object.values(CONFIRMATION_LEVELS)),
  /** Enable accessibility features */
  enableAccessibility: PropTypes.bool,
  /** Enable analytics tracking */
  enableAnalytics: PropTypes.bool,
  /** Enable keyboard shortcuts */
  enableKeyboardShortcuts: PropTypes.bool,
  /** Enable auto-timeout functionality */
  enableAutoTimeout: PropTypes.bool,
  /** Timeout duration in seconds */
  timeoutSeconds: PropTypes.number,
  /** Show progress indicator */
  showProgress: PropTypes.bool,
  /** Require double confirmation */
  requireDoubleConfirm: PropTypes.bool,
  /** Show keyboard hints */
  showKeyboardHints: PropTypes.bool,
  /** Custom icon override */
  customIcon: PropTypes.node,
  /** Timeout callback */
  onTimeout: PropTypes.func,
  /** Analytics tracking callback */
  onAnalyticsTrack: PropTypes.func,
  /** Dialog interaction callback */
  onDialogInteraction: PropTypes.func
};

export default ConfirmDialog;
