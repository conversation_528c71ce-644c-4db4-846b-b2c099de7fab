"""
Advanced credit management service with rollover capabilities and purchase system.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from ..core.config import settings

logger = logging.getLogger(__name__)

class CreditService:
    """Advanced credit management with rollover and purchase capabilities."""
    
    def __init__(self):
        self.credit_rates = {
            "regeneration": Decimal("1.0"),
            "ai_auto_reply": Decimal("0.1"),
            "image_generation": Decimal("2.0"),
            "document_training": Decimal("5.0"),
            "sentiment_analysis": Decimal("0.5"),
            # AI Response Management actions
            "ai_response_approval": Decimal("0.5"),
            "ai_response_regeneration": Decimal("1.5"),
            "ai_response_generation": Decimal("1.0")
        }
        
    async def get_credit_balance(self, user_id: str) -> Dict[str, Any]:
        """Get user's current credit balance with breakdown."""
        try:
            # Mock implementation - replace with actual database queries
            balance_data = {
                "total_balance": Decimal("25.5"),
                "purchased_credits": Decimal("20.0"),
                "bonus_credits": Decimal("5.0"),
                "rollover_credits": Decimal("0.5"),
                "expiring_credits": {
                    "amount": Decimal("2.0"),
                    "expiry_date": (datetime.now(timezone.utc) + timedelta(days=30)).isoformat()
                },
                "credit_history": [
                    {
                        "type": "purchase",
                        "amount": Decimal("20.0"),
                        "date": datetime.now(timezone.utc).isoformat(),
                        "description": "Credit purchase"
                    },
                    {
                        "type": "bonus",
                        "amount": Decimal("5.0"),
                        "date": (datetime.now(timezone.utc) - timedelta(days=1)).isoformat(),
                        "description": "Welcome bonus"
                    }
                ]
            }
            
            return self._serialize_decimals(balance_data)
        except Exception as e:
            logger.error(f"Error getting credit balance for user {user_id}: {e}")
            raise
            
    async def purchase_credits(self, user_id: str, amount: int, payment_method_id: Optional[str] = None) -> Dict[str, Any]:
        """Purchase credits with payment processing."""
        try:
            # Calculate cost based on bulk pricing
            cost_per_credit = self._get_credit_cost(amount)
            total_cost = Decimal(str(amount)) * cost_per_credit
            
            # Mock payment processing
            if payment_method_id:
                # Process payment immediately
                payment_result = await self._process_payment(user_id, total_cost, payment_method_id)
                if payment_result["success"]:
                    await self._add_credits(user_id, Decimal(str(amount)), "purchase")
                    return {
                        "success": True,
                        "credits_added": amount,
                        "cost": float(total_cost),
                        "payment_id": payment_result["payment_id"]
                    }
                else:
                    return {
                        "success": False,
                        "error": payment_result["error"]
                    }
            else:
                # Return payment URL for external processing
                payment_url = await self._create_payment_session(user_id, amount, total_cost)
                return {
                    "requires_payment": True,
                    "payment_url": payment_url,
                    "amount": amount,
                    "cost": float(total_cost)
                }
                
        except Exception as e:
            logger.error(f"Error purchasing credits for user {user_id}: {e}")
            raise
            
    async def transfer_credits(self, from_user_id: str, to_user_id: str, amount: Decimal) -> Dict[str, Any]:
        """Transfer credits between users."""
        try:
            # Check if sender has sufficient balance
            sender_balance = await self.get_credit_balance(from_user_id)
            if Decimal(str(sender_balance["total_balance"])) < amount:
                raise ValueError("Insufficient credit balance")
                
            # Perform transfer
            await self._deduct_credits(from_user_id, amount, "transfer_out", {"to_user": to_user_id})
            await self._add_credits(to_user_id, amount, "transfer_in", {"from_user": from_user_id})
            
            return {
                "success": True,
                "amount_transferred": float(amount),
                "from_user": from_user_id,
                "to_user": to_user_id
            }
            
        except Exception as e:
            logger.error(f"Error transferring credits: {e}")
            raise
            
    async def consume_credits(self, user_id: str, action: str, quantity: int = 1) -> Dict[str, Any]:
        """Consume credits for an action with tiered pricing."""
        try:
            # Calculate credit cost based on action and usage tier
            credit_cost = await self._calculate_action_cost(user_id, action, quantity)
            
            # Check if user has sufficient balance
            balance = await self.get_credit_balance(user_id)
            if Decimal(str(balance["total_balance"])) < credit_cost:
                return {
                    "success": False,
                    "error": "Insufficient credits",
                    "required": float(credit_cost),
                    "available": balance["total_balance"]
                }
                
            # Consume credits
            await self._deduct_credits(user_id, credit_cost, action, {"quantity": quantity})
            
            return {
                "success": True,
                "credits_consumed": float(credit_cost),
                "action": action,
                "quantity": quantity,
                "remaining_balance": float(Decimal(str(balance["total_balance"])) - credit_cost)
            }
            
        except Exception as e:
            logger.error(f"Error consuming credits for user {user_id}: {e}")
            raise
            
    async def process_credit_rollover(self, user_id: str) -> Dict[str, Any]:
        """Process monthly credit rollover based on subscription plan."""
        try:
            # Get user's subscription plan
            # Mock implementation
            plan_rollover_limits = {
                "explorer": 0,
                "creator": 5,
                "accelerator": 20,
                "dominator": 50
            }
            
            user_plan = "creator"  # Mock - get from subscription service
            rollover_limit = plan_rollover_limits.get(user_plan, 0)
            
            if rollover_limit > 0:
                # Calculate unused credits from previous month
                unused_credits = await self._get_unused_credits(user_id)
                rollover_amount = min(unused_credits, Decimal(str(rollover_limit)))
                
                if rollover_amount > 0:
                    await self._add_credits(user_id, rollover_amount, "rollover")
                    
                return {
                    "rollover_applied": True,
                    "amount": float(rollover_amount),
                    "limit": rollover_limit
                }
            else:
                return {
                    "rollover_applied": False,
                    "reason": "Plan does not support credit rollover"
                }
                
        except Exception as e:
            logger.error(f"Error processing credit rollover for user {user_id}: {e}")
            raise
            
    def _get_credit_cost(self, amount: int) -> Decimal:
        """Calculate cost per credit with bulk discounts."""
        if amount >= 100:
            return Decimal("0.80")  # 20% discount
        elif amount >= 50:
            return Decimal("0.90")  # 10% discount
        else:
            return Decimal("1.00")  # Regular price
            
    async def _calculate_action_cost(self, user_id: str, action: str, quantity: int) -> Decimal:
        """Calculate credit cost for an action with tiered pricing."""
        base_rate = self.credit_rates.get(action, Decimal("1.0"))
        
        if action == "regeneration":
            # Tiered pricing for regenerations
            usage_count = await self._get_monthly_usage_count(user_id, action)
            if usage_count == 0:
                return Decimal("0.0")  # First is free
            elif usage_count == 1:
                return Decimal("0.5")  # Second is discounted
            else:
                return base_rate  # Full price for additional
        else:
            return base_rate * Decimal(str(quantity))
            
    async def _process_payment(self, user_id: str, amount: Decimal, payment_method_id: str) -> Dict[str, Any]:
        """Process payment for credit purchase."""
        # Mock payment processing
        return {
            "success": True,
            "payment_id": f"pay_{user_id}_{int(datetime.now().timestamp())}"
        }
        
    async def _create_payment_session(self, user_id: str, credits: int, cost: Decimal) -> str:
        """Create payment session for credit purchase."""
        # Mock payment URL generation
        return f"https://payment.example.com/session/{user_id}/{credits}/{cost}"
        
    async def _add_credits(self, user_id: str, amount: Decimal, transaction_type: str, metadata: Optional[Dict[str, Any]] = None):
        """Add credits to user's account."""
        # Mock implementation - replace with actual database operations
        logger.info(f"Added {amount} credits to user {user_id} ({transaction_type})")
        
    async def _deduct_credits(self, user_id: str, amount: Decimal, transaction_type: str, metadata: Optional[Dict[str, Any]] = None):
        """Deduct credits from user's account."""
        # Mock implementation - replace with actual database operations
        logger.info(f"Deducted {amount} credits from user {user_id} ({transaction_type})")
        
    async def _get_unused_credits(self, user_id: str) -> Decimal:
        """Get unused credits from previous billing period."""
        # Mock implementation
        return Decimal("3.5")
        
    async def _get_monthly_usage_count(self, user_id: str, action: str) -> int:
        """Get monthly usage count for an action."""
        # Mock implementation
        return 2
        
    def _serialize_decimals(self, data: Any) -> Any:
        """Convert Decimal objects to float for JSON serialization."""
        if isinstance(data, dict):
            return {k: self._serialize_decimals(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._serialize_decimals(item) for item in data]
        elif isinstance(data, Decimal):
            return float(data)
        else:
            return data

# Global credit service instance
credit_service = CreditService()


# Wrapper functions for API usage
async def consume_credits(user_id: str, credit_cost: float, action: str, metadata: Optional[dict] = None) -> Dict[str, Any]:
    """
    Consume credits for a user action.

    Args:
        user_id (str): User ID
        credit_cost (float): Number of credits to consume
        action (str): Action being performed
        metadata (dict): Additional metadata for the transaction

    Returns:
        Dict with success status and remaining credits
    """
    # Convert to the format expected by the credit service
    quantity = int(credit_cost) if credit_cost >= 1 else 1
    result = await credit_service.consume_credits(user_id, action, quantity)

    # Add metadata to the result if provided
    if metadata:
        result["metadata"] = metadata

    return result


async def get_credit_balance(user_id: str) -> Dict[str, Any]:
    """
    Get user's current credit balance.

    Args:
        user_id (str): User ID

    Returns:
        Dict with credit balance information
    """
    return await credit_service.get_credit_balance(user_id)
