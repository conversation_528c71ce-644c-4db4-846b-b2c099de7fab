/**
 * Enhanced Trial Error Boundary - Enterprise-grade trial-specific error boundary component
 * Features: Comprehensive trial error boundary with advanced trial error handling, trial-specific
 * error recovery, and trial state preservation, detailed trial error customization with dynamic
 * trial error messaging and personalized trial recovery flows, advanced trial error features with
 * trial timeout handling and trial data recovery, ACE Social's trial system integration with seamless
 * trial error lifecycle management, trial error interaction features including trial-specific error
 * dismissal and trial recovery options, trial error state management with real-time trial error
 * updates and trial validation checks, real-time trial error updates with live trial error displays
 * and dynamic trial messaging, and seamless ACE Social trial platform integration with advanced
 * trial error orchestration and comprehensive trial accessibility compliance
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 */

import {
  Component,
  memo,
  useEffect,
  useCallback,
  useMemo
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Button,
  Alert,
  Paper,
  Stack,
  Chip,
  Collapse,
  LinearProgress,
  alpha,
  useTheme
} from '@mui/material';
import {
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  Home as HomeIcon,
  Support as SupportIcon,
  Warning as WarningIcon,
  Info as InfoIcon,

  Security as SecurityIcon,
  Upgrade as UpgradeIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material';


// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Trial error types
const TRIAL_ERROR_TYPES = {
  TRIAL_EXPIRED: 'trial_expired',
  TRIAL_LIMIT_EXCEEDED: 'trial_limit_exceeded',
  TRIAL_FEATURE_RESTRICTED: 'trial_feature_restricted',
  TRIAL_NETWORK_ERROR: 'trial_network_error',
  TRIAL_VALIDATION_ERROR: 'trial_validation_error',
  TRIAL_AUTHENTICATION_ERROR: 'trial_authentication_error',
  TRIAL_INTERNAL_ERROR: 'trial_internal_error'
};

// Trial error severity levels
const TRIAL_ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

class TrialErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: 0,
      trialErrorType: null,
      trialErrorSeverity: TRIAL_ERROR_SEVERITY.MEDIUM,
      showDetails: false,
      isRecovering: false,
      trialContext: null,
      errorAnalytics: {
        errorTime: null,
        userAgent: navigator.userAgent,
        trialStatus: null,
        lastActivity: null
      }
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Generate unique trial error ID for tracking
    const errorId = `TRIAL_ERR_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    // Determine trial error type and severity
    const trialErrorType = this.determineTrialErrorType(error);
    const trialErrorSeverity = this.determineTrialErrorSeverity(error, trialErrorType);

    // Get trial context if available
    const trialContext = this.getTrialContext();

    this.setState({
      error,
      errorInfo,
      errorId,
      trialErrorType,
      trialErrorSeverity,
      trialContext,
      errorAnalytics: {
        errorTime: new Date().toISOString(),
        userAgent: navigator.userAgent,
        trialStatus: trialContext?.isTrial ? 'active' : 'inactive',
        lastActivity: new Date().toISOString()
      }
    });

    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Trial Error Boundary caught an error:', error, errorInfo);
      console.error('Trial Error Type:', trialErrorType);
      console.error('Trial Context:', trialContext);
    }

    // Send trial error to monitoring service
    this.logTrialErrorToService(error, errorInfo, errorId, trialErrorType, trialContext);
  }

  // Trial-specific helper methods
  determineTrialErrorType = (error) => {
    const message = error.message.toLowerCase();

    if (message.includes('trial') && message.includes('expired')) {
      return TRIAL_ERROR_TYPES.TRIAL_EXPIRED;
    } else if (message.includes('limit') && message.includes('exceeded')) {
      return TRIAL_ERROR_TYPES.TRIAL_LIMIT_EXCEEDED;
    } else if (message.includes('feature') && message.includes('restricted')) {
      return TRIAL_ERROR_TYPES.TRIAL_FEATURE_RESTRICTED;
    } else if (message.includes('network') || message.includes('fetch')) {
      return TRIAL_ERROR_TYPES.TRIAL_NETWORK_ERROR;
    } else if (message.includes('validation') || message.includes('invalid')) {
      return TRIAL_ERROR_TYPES.TRIAL_VALIDATION_ERROR;
    } else if (message.includes('auth') || message.includes('unauthorized')) {
      return TRIAL_ERROR_TYPES.TRIAL_AUTHENTICATION_ERROR;
    }

    return TRIAL_ERROR_TYPES.TRIAL_INTERNAL_ERROR;
  };

  determineTrialErrorSeverity = (error, trialErrorType) => {
    switch (trialErrorType) {
      case TRIAL_ERROR_TYPES.TRIAL_EXPIRED:
      case TRIAL_ERROR_TYPES.TRIAL_AUTHENTICATION_ERROR:
        return TRIAL_ERROR_SEVERITY.CRITICAL;
      case TRIAL_ERROR_TYPES.TRIAL_LIMIT_EXCEEDED:
      case TRIAL_ERROR_TYPES.TRIAL_FEATURE_RESTRICTED:
        return TRIAL_ERROR_SEVERITY.HIGH;
      case TRIAL_ERROR_TYPES.TRIAL_NETWORK_ERROR:
      case TRIAL_ERROR_TYPES.TRIAL_VALIDATION_ERROR:
        return TRIAL_ERROR_SEVERITY.MEDIUM;
      default:
        return TRIAL_ERROR_SEVERITY.LOW;
    }
  };

  getTrialContext = () => {
    try {
      // Try to get trial context from localStorage or other sources
      const trialData = localStorage.getItem('trialContext');
      return trialData ? JSON.parse(trialData) : null;
    } catch (err) {
      return null;
    }
  };

  logTrialErrorToService = (error, errorInfo, errorId, trialErrorType, trialContext) => {
    try {
      // Enhanced trial error data for monitoring service
      const trialErrorData = {
        errorId,
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: localStorage.getItem('userId') || 'anonymous',
        // Trial-specific data
        trialErrorType,
        trialErrorSeverity: this.state.trialErrorSeverity,
        trialContext: {
          isTrial: trialContext?.isTrial || false,
          daysRemaining: trialContext?.daysRemaining || 0,
          hoursRemaining: trialContext?.hoursRemaining || 0,
          planId: trialContext?.planId || 'unknown',
          trialEnd: trialContext?.trialEnd || null
        },
        errorAnalytics: this.state.errorAnalytics
      };

      // Send to trial error logging endpoint
      fetch('/api/trial-errors/log', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(trialErrorData)
      }).catch(err => {
        console.error('Failed to log trial error to service:', err);
      });
    } catch (err) {
      console.error('Error in trial error logging:', err);
    }
  };

  // Enhanced trial-specific handlers
  handleTrialRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      trialErrorType: null,
      trialErrorSeverity: TRIAL_ERROR_SEVERITY.MEDIUM,
      showDetails: false,
      isRecovering: false,
      retryCount: prevState.retryCount + 1
    }));
  };

  handleTrialUpgrade = () => {
    // Navigate to trial upgrade page
    window.location.href = '/billing/plans?source=trial_error';
  };

  handleTrialSupport = () => {
    const { error, errorId, trialErrorType, trialContext } = this.state;
    const subject = `Trial Error Report - ${errorId}`;
    const body = `Trial Error ID: ${errorId}\nError Type: ${trialErrorType}\nError: ${error?.message}\nTrial Status: ${trialContext?.isTrial ? 'Active' : 'Inactive'}\nDays Remaining: ${trialContext?.daysRemaining || 'N/A'}\nURL: ${window.location.href}\nTime: ${new Date().toISOString()}`;

    window.open(`mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
  };

  handleTrialRecover = () => {
    const { trialErrorType } = this.state;

    this.setState({ isRecovering: true });

    // Trial-specific recovery actions
    switch (trialErrorType) {
      case TRIAL_ERROR_TYPES.TRIAL_EXPIRED:
        this.handleTrialUpgrade();
        break;
      case TRIAL_ERROR_TYPES.TRIAL_LIMIT_EXCEEDED:
        this.handleTrialUpgrade();
        break;
      case TRIAL_ERROR_TYPES.TRIAL_FEATURE_RESTRICTED:
        this.handleTrialUpgrade();
        break;
      case TRIAL_ERROR_TYPES.TRIAL_NETWORK_ERROR:
        setTimeout(() => {
          this.handleTrialRetry();
        }, 2000);
        break;
      case TRIAL_ERROR_TYPES.TRIAL_AUTHENTICATION_ERROR:
        window.location.href = '/login?source=trial_error';
        break;
      default:
        this.handleTrialRetry();
        break;
    }
  };

  toggleDetails = () => {
    this.setState(prevState => ({
      showDetails: !prevState.showDetails
    }));
  };

  render() {
    if (this.state.hasError) {
      const {
        error,
        errorId,
        retryCount,
        trialErrorType,
        trialErrorSeverity,
        showDetails,
        isRecovering,
        trialContext
      } = this.state;
      const { fallback: CustomFallback } = this.props;

      // If a custom fallback is provided, use it
      if (CustomFallback) {
        return (
          <CustomFallback
            error={error}
            errorId={errorId}
            trialErrorType={trialErrorType}
            trialErrorSeverity={trialErrorSeverity}
            trialContext={trialContext}
            onRetry={this.handleTrialRetry}
            onUpgrade={this.handleTrialUpgrade}
            onSupport={this.handleTrialSupport}
            onRecover={this.handleTrialRecover}
          />
        );
      }

      // Enhanced trial error UI with Material-UI
      return (
        <TrialErrorFallback
          error={error}
          errorId={errorId}
          trialErrorType={trialErrorType}
          trialErrorSeverity={trialErrorSeverity}
          trialContext={trialContext}
          retryCount={retryCount}
          showDetails={showDetails}
          isRecovering={isRecovering}
          onRetry={this.handleTrialRetry}
          onUpgrade={this.handleTrialUpgrade}
          onSupport={this.handleTrialSupport}
          onRecover={this.handleTrialRecover}
          onToggleDetails={this.toggleDetails}
        />
      );
    }

    return this.props.children;
  }
}

// Enhanced Trial Error Fallback Component
const TrialErrorFallback = memo(({
  error,
  errorId,
  trialErrorType,
  trialErrorSeverity,
  trialContext,
  retryCount,
  showDetails,
  isRecovering,
  onRetry,
  onUpgrade,
  onSupport,

  onToggleDetails
}) => {
  const theme = useTheme();
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Trial error configuration
  const errorConfig = useMemo(() => {
    const configs = {
      [TRIAL_ERROR_TYPES.TRIAL_EXPIRED]: {
        icon: <WarningIcon sx={{ fontSize: 48, color: ACE_COLORS.YELLOW }} />,
        title: 'Trial Expired',
        description: 'Your trial has expired. Upgrade to continue using premium features.',
        severity: 'warning',
        primaryAction: 'Upgrade Now',
        primaryHandler: onUpgrade
      },
      [TRIAL_ERROR_TYPES.TRIAL_LIMIT_EXCEEDED]: {
        icon: <InfoIcon sx={{ fontSize: 48, color: ACE_COLORS.PURPLE }} />,
        title: 'Trial Limit Reached',
        description: 'You\'ve reached your trial usage limit. Upgrade for unlimited access.',
        severity: 'info',
        primaryAction: 'Upgrade Plan',
        primaryHandler: onUpgrade
      },
      [TRIAL_ERROR_TYPES.TRIAL_FEATURE_RESTRICTED]: {
        icon: <SecurityIcon sx={{ fontSize: 48, color: ACE_COLORS.PURPLE }} />,
        title: 'Feature Restricted',
        description: 'This feature is not available in your trial. Upgrade to unlock all features.',
        severity: 'info',
        primaryAction: 'Upgrade Now',
        primaryHandler: onUpgrade
      },
      [TRIAL_ERROR_TYPES.TRIAL_NETWORK_ERROR]: {
        icon: <ErrorIcon sx={{ fontSize: 48, color: '#F44336' }} />,
        title: 'Connection Error',
        description: 'Unable to connect to trial services. Please check your connection.',
        severity: 'error',
        primaryAction: 'Retry',
        primaryHandler: onRetry
      },
      [TRIAL_ERROR_TYPES.TRIAL_AUTHENTICATION_ERROR]: {
        icon: <SecurityIcon sx={{ fontSize: 48, color: '#F44336' }} />,
        title: 'Authentication Error',
        description: 'Your trial session has expired. Please log in again.',
        severity: 'error',
        primaryAction: 'Log In',
        primaryHandler: () => window.location.href = '/login'
      }
    };
    return configs[trialErrorType] || {
      icon: <ErrorIcon sx={{ fontSize: 48, color: '#F44336' }} />,
      title: 'Trial Error',
      description: 'An unexpected error occurred during your trial.',
      severity: 'error',
      primaryAction: 'Retry',
      primaryHandler: onRetry
    };
  }, [trialErrorType, onUpgrade, onRetry]);

  useEffect(() => {
    if (announceToScreenReader) {
      announceToScreenReader(`Trial error occurred: ${errorConfig.title}. ${errorConfig.description}`);
    }
  }, [announceToScreenReader, errorConfig]);

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 3,
        backgroundColor: alpha(ACE_COLORS.DARK, 0.05)
      }}
    >
      <Paper
        elevation={8}
        sx={{
          ...glassMorphismStyles,
          maxWidth: 600,
          width: '100%',
          p: 4,
          textAlign: 'center'
        }}
      >
        {/* Error Icon and Title */}
        <Box sx={{ mb: 3 }}>
          {errorConfig.icon}
          <Typography variant="h4" component="h1" sx={{ mt: 2, mb: 1, fontWeight: 'bold' }}>
            {errorConfig.title}
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
            {errorConfig.description}
          </Typography>
        </Box>

        {/* Trial Status Alert */}
        {trialContext?.isTrial && (
          <Alert severity="info" sx={{ mb: 3, textAlign: 'left' }}>
            <Typography variant="body2">
              <strong>Trial Status:</strong> {trialContext.daysRemaining} days remaining
              {trialContext.trialEnd && (
                <span> (expires {new Date(trialContext.trialEnd).toLocaleDateString()})</span>
              )}
            </Typography>
          </Alert>
        )}

        {/* Error Severity Chip */}
        <Box sx={{ mb: 3 }}>
          <Chip
            label={`${trialErrorSeverity.toUpperCase()} SEVERITY`}
            color={trialErrorSeverity === TRIAL_ERROR_SEVERITY.CRITICAL ? 'error' :
                   trialErrorSeverity === TRIAL_ERROR_SEVERITY.HIGH ? 'warning' : 'info'}
            variant="outlined"
          />
        </Box>

        {/* Error ID */}
        {errorId && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="caption" color="text.secondary">
              Error ID: <code>{errorId}</code>
            </Typography>
          </Box>
        )}

        {/* Recovery Progress */}
        {isRecovering && (
          <Box sx={{ mb: 3 }}>
            <LinearProgress sx={{ mb: 1 }} />
            <Typography variant="body2" color="text.secondary">
              Attempting recovery...
            </Typography>
          </Box>
        )}

        {/* Action Buttons */}
        <Stack spacing={2} sx={{ mb: 3 }}>
          <Button
            variant="contained"
            size="large"
            onClick={errorConfig.primaryHandler}
            disabled={isRecovering || (retryCount >= 3 && errorConfig.primaryAction === 'Retry')}
            startIcon={errorConfig.primaryAction === 'Upgrade Now' || errorConfig.primaryAction === 'Upgrade Plan' ?
              <UpgradeIcon /> : <RefreshIcon />}
            sx={{
              backgroundColor: ACE_COLORS.PURPLE,
              '&:hover': {
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8)
              }
            }}
          >
            {retryCount >= 3 && errorConfig.primaryAction === 'Retry' ?
              'Max retries reached' : errorConfig.primaryAction}
          </Button>

          <Stack direction="row" spacing={2}>
            <Button
              variant="outlined"
              onClick={onSupport}
              startIcon={<SupportIcon />}
              disabled={isRecovering}
            >
              Contact Support
            </Button>
            <Button
              variant="outlined"
              onClick={() => window.location.href = '/'}
              startIcon={<HomeIcon />}
              disabled={isRecovering}
            >
              Go Home
            </Button>
          </Stack>
        </Stack>

        {/* Error Details Toggle */}
        <Box>
          <Button
            variant="text"
            size="small"
            onClick={onToggleDetails}
            endIcon={showDetails ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          >
            {showDetails ? 'Hide' : 'Show'} Error Details
          </Button>

          <Collapse in={showDetails}>
            <Box sx={{ mt: 2, p: 2, backgroundColor: alpha('#000', 0.05), borderRadius: 1 }}>
              {process.env.NODE_ENV === 'development' && error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  <Typography variant="body2" component="div">
                    <strong>Development Info:</strong>
                  </Typography>
                  <Typography variant="caption" component="pre" sx={{ fontFamily: 'monospace' }}>
                    {error.message}
                  </Typography>
                </Alert>
              )}
              <Typography variant="caption" color="text.secondary">
                If this problem persists, please contact our support team with the error ID above.
              </Typography>
            </Box>
          </Collapse>
        </Box>
      </Paper>
    </Box>
  );
});

TrialErrorFallback.displayName = 'TrialErrorFallback';

TrialErrorFallback.propTypes = {
  error: PropTypes.object,
  errorId: PropTypes.string,
  trialErrorType: PropTypes.string,
  trialErrorSeverity: PropTypes.string,
  trialContext: PropTypes.object,
  retryCount: PropTypes.number,
  showDetails: PropTypes.bool,
  isRecovering: PropTypes.bool,
  onRetry: PropTypes.func.isRequired,
  onUpgrade: PropTypes.func.isRequired,
  onSupport: PropTypes.func.isRequired,
  onToggleDetails: PropTypes.func.isRequired
};

// Higher-order component for wrapping components with trial error boundary
export const withTrialErrorBoundary = (Component, fallback = null) => {
  return function WrappedComponent(props) {
    return (
      <TrialErrorBoundary fallback={fallback}>
        <Component {...props} />
      </TrialErrorBoundary>
    );
  };
};

// Hook for trial error recovery suggestions
export const useTrialErrorRecovery = () => {
  const getTrialRecoverySuggestion = (trialErrorType) => {
    const suggestions = {
      [TRIAL_ERROR_TYPES.TRIAL_EXPIRED]: 'Your trial has expired. Upgrade to a paid plan to continue using premium features.',
      [TRIAL_ERROR_TYPES.TRIAL_LIMIT_EXCEEDED]: 'You\'ve reached your trial usage limit. Upgrade for unlimited access.',
      [TRIAL_ERROR_TYPES.TRIAL_FEATURE_RESTRICTED]: 'This feature is not available in your trial. Upgrade to unlock all features.',
      [TRIAL_ERROR_TYPES.TRIAL_NETWORK_ERROR]: 'Check your internet connection and try again.',
      [TRIAL_ERROR_TYPES.TRIAL_VALIDATION_ERROR]: 'Please check your trial input and try again.',
      [TRIAL_ERROR_TYPES.TRIAL_AUTHENTICATION_ERROR]: 'Your trial session has expired. Please log in again.',
      [TRIAL_ERROR_TYPES.TRIAL_INTERNAL_ERROR]: 'An unexpected trial error occurred. Please try again.'
    };

    return suggestions[trialErrorType] || 'Please try again or contact support if the trial problem persists.';
  };

  const getTrialActionButton = (trialErrorType) => {
    const actions = {
      [TRIAL_ERROR_TYPES.TRIAL_EXPIRED]: { text: 'Upgrade Now', action: () => window.location.href = '/billing/plans' },
      [TRIAL_ERROR_TYPES.TRIAL_LIMIT_EXCEEDED]: { text: 'Upgrade Plan', action: () => window.location.href = '/billing/plans' },
      [TRIAL_ERROR_TYPES.TRIAL_FEATURE_RESTRICTED]: { text: 'Upgrade Now', action: () => window.location.href = '/billing/plans' },
      [TRIAL_ERROR_TYPES.TRIAL_NETWORK_ERROR]: { text: 'Retry', action: () => window.location.reload() },
      [TRIAL_ERROR_TYPES.TRIAL_AUTHENTICATION_ERROR]: { text: 'Log In', action: () => window.location.href = '/login' },
      [TRIAL_ERROR_TYPES.TRIAL_VALIDATION_ERROR]: { text: 'Try Again', action: () => window.location.reload() }
    };

    return actions[trialErrorType] || { text: 'Try Again', action: () => window.location.reload() };
  };

  return { getTrialRecoverySuggestion, getTrialActionButton };
};

export default TrialErrorBoundary;
