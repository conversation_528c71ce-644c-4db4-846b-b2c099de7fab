"""
Enhanced email service with additional email types.
"""

from typing import List, Optional, Dict, Any
import logging
from datetime import datetime, timedelta, timezone
from app.core.config import settings
from app.services.app_logging import get_logger

# Set up logger
logger = get_logger(__name__)

# Import email_service from a different module to avoid circular imports
from app.services.email_service import email_service

async def send_welcome_email(email: str, name: str) -> bool:
    """
    Send a welcome email to a new user.

    Args:
        email: User's email address
        name: User's name

    Returns:
        True if email was sent successfully, False otherwise
    """
    return await email_service.send_email(
        recipient_email=email,
        subject="Welcome to ACE Social",
        template_name="welcome",
        template_data={
            "name": name,
            "login_url": f"{settings.FRONTEND_URL}/login"
        },
        recipient_name=name,
        categories=["onboarding", "welcome"]
    )

async def send_password_reset_email(email: str, name: str, token: str) -> bool:
    """
    Send a password reset email.

    Args:
        email: User's email address
        name: User's name
        token: Password reset token

    Returns:
        True if email was sent successfully, False otherwise
    """
    reset_url = f"{settings.FRONTEND_URL}/reset-password?token={token}"

    return await email_service.send_email(
        recipient_email=email,
        subject="Reset Your Password",
        template_name="password_reset",
        template_data={
            "name": name,
            "reset_url": reset_url,
            "expires_in": "1 hour"
        },
        recipient_name=name,
        categories=["account", "password_reset"]
    )

async def send_magic_link_email(email: str, name: str, token: str) -> bool:
    """
    Send a magic link email for passwordless login.

    Args:
        email: User's email address
        name: User's name
        token: Magic link token

    Returns:
        True if email was sent successfully, False otherwise
    """
    magic_link_url = f"{settings.FRONTEND_URL}/auth/magic-link?token={token}"

    return await email_service.send_email(
        recipient_email=email,
        subject="Your Magic Link to Sign In",
        template_name="magic_link",
        template_data={
            "name": name,
            "magic_link_url": magic_link_url,
            "expires_in": "15 minutes"
        },
        recipient_name=name,
        categories=["account", "magic_link"]
    )

async def send_email_verification(email: str, name: str, token: str) -> bool:
    """
    Send an email verification link.

    Args:
        email: User's email address
        name: User's name
        token: Email verification token

    Returns:
        True if email was sent successfully, False otherwise
    """
    verification_url = f"{settings.FRONTEND_URL}/verify-email?token={token}"

    return await email_service.send_email(
        recipient_email=email,
        subject="Verify Your Email Address",
        template_name="email_verification",
        template_data={
            "name": name,
            "verification_url": verification_url,
            "expires_in": "48 hours"
        },
        recipient_name=name,
        categories=["account", "email_verification"]
    )

async def send_subscription_state_change_email(
    email: str,
    first_name: str,
    event_type: str,
    **kwargs
) -> bool:
    """
    Send an email for subscription state changes.

    Args:
        email: Recipient's email address
        first_name: Recipient's first name
        event_type: Type of event (e.g., subscription_activated, payment_succeeded)
        **kwargs: Additional data for the email template

    Returns:
        True if email was sent successfully, False otherwise
    """
    return await email_service.send_subscription_state_change_email(
        email=email,
        first_name=first_name,
        event_type=event_type,
        **kwargs
    )

async def send_admin_notification_email(
    subject: str,
    message: str
) -> bool:
    """
    Send a notification email to administrators.

    Args:
        subject: Email subject
        message: Email message

    Returns:
        True if email was sent successfully, False otherwise
    """
    return await email_service.send_admin_notification_email(
        subject=subject,
        message=message
    )

async def send_account_lockout_email(
    email: str,
    name: str,
    lockout_time: int,
    ip_address: str,
    attempts: int
) -> bool:
    """
    Send an email when an account is locked due to too many failed login attempts.

    Args:
        email: User's email address
        name: User's name
        lockout_time: Lockout time in seconds
        ip_address: IP address of the login attempts
        attempts: Number of failed attempts

    Returns:
        True if email was sent successfully, False otherwise
    """
    # Convert lockout time to minutes for better readability
    lockout_minutes = max(1, round(lockout_time / 60))

    # Create the reset password URL
    reset_url = f"{settings.FRONTEND_URL}/reset-password"

    return await email_service.send_email(
        recipient_email=email,
        subject="Account Security Alert: Login Attempts Detected",
        template_name="account_lockout",
        template_data={
            "name": name,
            "ip_address": ip_address,
            "attempts": attempts,
            "lockout_minutes": lockout_minutes,
            "reset_url": reset_url
        },
        recipient_name=name,
        categories=["security", "account_lockout"]
    )

async def send_calendar_invitation_email(
    recipient_email: str,
    recipient_name: str,
    event_title: str,
    event_description: str,
    event_start: datetime,
    event_end: datetime,
    event_location: Optional[str] = None,
    organizer_name: Optional[str] = None,
    organizer_email: Optional[str] = None,
    calendar_url: Optional[str] = None,
    ical_attachment: Optional[bytes] = None
) -> bool:
    """
    Send a calendar invitation email.

    Args:
        recipient_email: Recipient's email address
        recipient_name: Recipient's name
        event_title: Title of the event
        event_description: Description of the event
        event_start: Start time of the event
        event_end: End time of the event
        event_location: Optional location of the event
        organizer_name: Optional name of the organizer
        organizer_email: Optional email of the organizer
        calendar_url: Optional URL to view the event in a calendar
        ical_attachment: Optional iCalendar attachment

    Returns:
        True if email was sent successfully, False otherwise
    """
    # Format dates for display
    formatted_start = event_start.strftime("%A, %B %d, %Y at %I:%M %p")
    formatted_end = event_end.strftime("%I:%M %p")

    # If the event spans multiple days, include the date in the end time
    if event_start.date() != event_end.date():
        formatted_end = event_end.strftime("%A, %B %d, %Y at %I:%M %p")

    # Create the calendar URL if not provided
    if not calendar_url:
        calendar_url = f"{settings.FRONTEND_URL}/calendar"

    return await email_service.send_email(
        recipient_email=recipient_email,
        subject=f"Calendar Invitation: {event_title}",
        template_name="calendar_invitation",
        template_data={
            "recipient_name": recipient_name,
            "event_title": event_title,
            "event_description": event_description,
            "event_start": formatted_start,
            "event_end": formatted_end,
            "event_location": event_location,
            "organizer_name": organizer_name or "B2B Influencer Tool",
            "organizer_email": organizer_email or settings.EMAILS_FROM_EMAIL,
            "calendar_url": calendar_url
        },
        recipient_name=recipient_name,
        categories=["calendar", "invitation"]
    )

async def send_trial_started_email(
    email: str,
    name: str,
    trial_end_date: datetime,
    plan_name: str
) -> bool:
    """
    Send an email when a trial period starts.

    Args:
        email: User's email address
        name: User's name
        trial_end_date: Date when the trial ends
        plan_name: Name of the plan being tried

    Returns:
        True if email was sent successfully, False otherwise
    """
    # Format the trial end date
    formatted_end_date = trial_end_date.strftime("%B %d, %Y")

    # Calculate days remaining
    days_remaining = (trial_end_date - datetime.now(timezone.utc)).days

    return await email_service.send_email(
        recipient_email=email,
        subject=f"Your {plan_name} Trial Has Started",
        template_name="trial_started",
        template_data={
            "name": name,
            "plan_name": plan_name,
            "trial_end_date": formatted_end_date,
            "days_remaining": days_remaining,
            "billing_url": f"{settings.FRONTEND_URL}/billing"
        },
        recipient_name=name,
        categories=["trial", "onboarding"]
    )

async def send_trial_ending_email(
    email: str,
    name: str,
    days_remaining: int,
    trial_end_date: datetime,
    plan_name: str
) -> bool:
    """
    Send an email when a trial period is about to end.

    Args:
        email: User's email address
        name: User's name
        days_remaining: Number of days remaining in the trial
        trial_end_date: Date when the trial ends
        plan_name: Name of the plan being tried

    Returns:
        True if email was sent successfully, False otherwise
    """
    # Format the trial end date
    formatted_end_date = trial_end_date.strftime("%B %d, %Y")

    return await email_service.send_email(
        recipient_email=email,
        subject=f"Your {plan_name} Trial Ends in {days_remaining} Days",
        template_name="trial_ending",
        template_data={
            "name": name,
            "plan_name": plan_name,
            "trial_end_date": formatted_end_date,
            "days_remaining": days_remaining,
            "billing_url": f"{settings.FRONTEND_URL}/billing"
        },
        recipient_name=name,
        categories=["trial", "billing"]
    )

async def send_trial_ending_soon_email(
    user_email: str,
    user_name: str,
    trial_end_date: datetime,
    days_remaining: int,
    plan_name: str
) -> bool:
    """
    Send an email when a trial period is about to end.

    Args:
        user_email: User's email address
        user_name: User's name
        trial_end_date: Date when the trial ends
        days_remaining: Number of days remaining in the trial
        plan_name: Name of the plan being tried

    Returns:
        True if email was sent successfully, False otherwise
    """
    # Format the trial end date
    formatted_end_date = trial_end_date.strftime("%B %d, %Y")

    return await email_service.send_email(
        recipient_email=user_email,
        subject=f"Your {plan_name} Trial Ends in {days_remaining} Days",
        template_name="trial_ending_soon",
        template_data={
            "name": user_name,
            "plan_name": plan_name,
            "trial_end_date": formatted_end_date,
            "days_remaining": days_remaining,
            "billing_url": f"{settings.FRONTEND_URL}/billing"
        },
        recipient_name=user_name,
        categories=["trial", "billing"]
    )

async def send_trial_ended_email(
    email: str,
    name: str,
    plan_name: str,
    subscription_active: bool = False
) -> bool:
    """
    Send an email when a trial period has ended.

    Args:
        email: User's email address
        name: User's name
        plan_name: Name of the plan that was tried
        subscription_active: Whether the user has an active subscription

    Returns:
        True if email was sent successfully, False otherwise
    """
    if subscription_active:
        subject = f"Your {plan_name} Subscription is Now Active"
        template = "trial_converted"
    else:
        subject = f"Your {plan_name} Trial Has Ended"
        template = "trial_ended"

    return await email_service.send_email(
        recipient_email=email,
        subject=subject,
        template_name=template,
        template_data={
            "name": name,
            "plan_name": plan_name,
            "subscription_active": subscription_active,
            "billing_url": f"{settings.FRONTEND_URL}/billing"
        },
        recipient_name=name,
        categories=["trial", "billing"]
    )

async def send_team_invitation_email(
    recipient_email: str,
    recipient_name: str,
    team_name: str,
    inviter_name: str,
    invitation_token: str,
    expiration_hours: int = 168  # 7 days default
) -> bool:
    """
    Send a team invitation email with enhanced support for new and existing users.

    Args:
        recipient_email: Recipient's email address
        recipient_name: Recipient's name
        team_name: Name of the team
        inviter_name: Name of the person who sent the invitation
        invitation_token: Token for accepting the invitation
        expiration_hours: Hours until the invitation expires (default: 7 days)

    Returns:
        True if email was sent successfully, False otherwise
    """
    # Check if user already exists
    from app.services.user import get_user_by_email

    try:
        existing_user = await get_user_by_email(recipient_email)
        user_exists = existing_user is not None
    except Exception:
        user_exists = False

    # Create different URLs based on user existence
    if user_exists:
        # Existing user - direct to invitation acceptance
        invitation_url = f"{settings.FRONTEND_URL}/teams/accept-invitation?token={invitation_token}"
        register_url = None
    else:
        # New user - direct to registration with invitation
        invitation_url = f"{settings.FRONTEND_URL}/teams/accept-invitation?token={invitation_token}"
        register_url = f"{settings.FRONTEND_URL}/register?invitation={invitation_token}"

    return await email_service.send_email(
        recipient_email=recipient_email,
        subject=f"🎉 You're Invited to Join {team_name} on ACE Social",
        template_name="team_invitation_enhanced",
        template_data={
            "recipient_name": recipient_name,
            "team_name": team_name,
            "inviter_name": inviter_name,
            "invitation_url": invitation_url,
            "register_url": register_url,
            "user_exists": user_exists,
            "expiration_hours": expiration_hours,
            "expiration_days": expiration_hours // 24,
            "platform_name": "ACE Social",
            "support_email": getattr(settings, 'SUPPORT_EMAIL', "<EMAIL>")
        },
        recipient_name=recipient_name,
        categories=["teams", "invitation", "enhanced"]
    )

async def send_payment_failed_email(
    user_email: str,
    user_name: str,
    amount: float,
    currency: str,
    attempt_date: datetime,
    failure_reason: str
) -> bool:
    """
    Send an email when a payment fails.

    Args:
        user_email: User's email address
        user_name: User's name
        amount: Payment amount
        currency: Currency code (e.g., USD)
        attempt_date: Date of the payment attempt
        failure_reason: Reason for the payment failure

    Returns:
        True if email was sent successfully, False otherwise
    """
    # Format the attempt date
    formatted_date = attempt_date.strftime("%B %d, %Y")

    # Create the billing URL
    billing_url = f"{settings.FRONTEND_URL}/billing/payment-methods"

    return await email_service.send_email(
        recipient_email=user_email,
        subject="Payment Failed - Action Required",
        template_name="payment_failed",
        template_data={
            "user_name": user_name,
            "amount": amount,
            "currency": currency.upper(),
            "attempt_date": formatted_date,
            "failure_reason": failure_reason,
            "billing_url": billing_url
        },
        recipient_name=user_name,
        categories=["billing", "payment_failed"]
    )

async def send_payment_succeeded_email(
    user_email: str,
    user_name: str,
    amount: float,
    currency: str,
    invoice_url: Optional[str] = None,
    plan_name: Optional[str] = None
) -> bool:
    """
    Send an email when a payment succeeds.

    Args:
        user_email: User's email address
        user_name: User's name
        amount: Payment amount
        currency: Payment currency
        invoice_url: Optional URL to view the invoice
        plan_name: Optional name of the plan

    Returns:
        True if email was sent successfully, False otherwise
    """
    # Format the amount with 2 decimal places
    formatted_amount = f"{amount:.2f}"

    # Create the subject line
    subject = "Payment Successful"
    if plan_name:
        subject = f"Payment Successful for {plan_name} Plan"

    return await email_service.send_email(
        recipient_email=user_email,
        subject=subject,
        template_name="payment_succeeded",
        template_data={
            "name": user_name,
            "amount": formatted_amount,
            "currency": currency.upper(),
            "invoice_url": invoice_url,
            "plan_name": plan_name,
            "billing_url": f"{settings.FRONTEND_URL}/billing"
        },
        recipient_name=user_name,
        categories=["billing", "payment_succeeded"]
    )

async def send_subscription_created_email(
    user_email: str,
    user_name: str,
    plan_name: str,
    amount: float,
    currency: str,
    is_yearly: bool = False
) -> bool:
    """
    Send an email when a subscription is created.

    Args:
        user_email: User's email address
        user_name: User's name
        plan_name: Name of the plan
        amount: Subscription amount
        currency: Currency code
        is_yearly: Whether the subscription is yearly

    Returns:
        True if email was sent successfully, False otherwise
    """
    # Format the amount with 2 decimal places
    formatted_amount = f"{amount:.2f}"

    # Create the billing period text
    billing_period = "yearly" if is_yearly else "monthly"

    return await email_service.send_email(
        recipient_email=user_email,
        subject=f"Welcome to Your {plan_name} Subscription",
        template_name="subscription_created",
        template_data={
            "name": user_name,
            "plan_name": plan_name,
            "amount": formatted_amount,
            "currency": currency.upper(),
            "billing_period": billing_period,
            "billing_url": f"{settings.FRONTEND_URL}/billing",
            "dashboard_url": f"{settings.FRONTEND_URL}/dashboard"
        },
        recipient_name=user_name,
        categories=["billing", "subscription"]
    )

async def send_subscription_cancelled_email(
    user_email: str,
    user_name: str,
    plan_name: str,
    cancellation_date: datetime,
    access_until_date: datetime
) -> bool:
    """
    Send an email when a subscription is cancelled.

    Args:
        user_email: User's email address
        user_name: User's name
        plan_name: Name of the subscription plan
        cancellation_date: Date when the subscription was cancelled
        access_until_date: Date until which the user will have access

    Returns:
        True if email was sent successfully, False otherwise
    """
    # Format dates
    formatted_cancellation_date = cancellation_date.strftime("%B %d, %Y")
    formatted_access_until_date = access_until_date.strftime("%B %d, %Y")

    # Create URLs
    feedback_url = f"{settings.FRONTEND_URL}/feedback?type=cancellation"
    reactivate_url = f"{settings.FRONTEND_URL}/billing/reactivate"

    return await email_service.send_email(
        recipient_email=user_email,
        subject=f"Your {plan_name} Subscription Has Been Cancelled",
        template_name="subscription_cancelled",
        template_data={
            "user_name": user_name,
            "plan_name": plan_name,
            "cancellation_date": formatted_cancellation_date,
            "access_until_date": formatted_access_until_date,
            "feedback_url": feedback_url,
            "reactivate_url": reactivate_url
        },
        recipient_name=user_name,
        categories=["billing", "subscription_cancelled"]
    )

# Alias for American spelling
async def send_subscription_canceled_email(
    user_email: str,
    user_name: str,
    plan_name: str,
    cancellation_date: datetime,
    access_until_date: datetime
) -> bool:
    """Alias for send_subscription_cancelled_email with American spelling."""
    return await send_subscription_cancelled_email(
        user_email=user_email,
        user_name=user_name,
        plan_name=plan_name,
        cancellation_date=cancellation_date,
        access_until_date=access_until_date
    )

async def send_subscription_updated_email(
    user_email: str,
    user_name: str,
    old_plan_name: str,
    new_plan_name: str,
    amount: float,
    currency: str,
    is_upgrade: bool = True,
    effective_date: Optional[datetime] = None,
    feature_changes: Optional[Dict[str, Any]] = None
) -> bool:
    """
    Send an email when a subscription is updated.

    Args:
        user_email: User's email address
        user_name: User's name
        old_plan_name: Name of the old plan
        new_plan_name: Name of the new plan
        amount: New subscription amount
        currency: Currency code
        is_upgrade: Whether this is an upgrade or downgrade
        effective_date: When the change takes effect
        feature_changes: Dictionary of feature changes

    Returns:
        True if email was sent successfully, False otherwise
    """
    # Format the amount with 2 decimal places
    formatted_amount = f"{amount:.2f}"

    # Format the effective date if provided
    formatted_effective_date = None
    if effective_date:
        formatted_effective_date = effective_date.strftime("%B %d, %Y")

    # Create the subject line
    action = "Upgraded" if is_upgrade else "Changed"
    subject = f"Your Subscription Has Been {action} to {new_plan_name}"

    return await email_service.send_email(
        recipient_email=user_email,
        subject=subject,
        template_name="subscription_updated",
        template_data={
            "name": user_name,
            "old_plan_name": old_plan_name,
            "new_plan_name": new_plan_name,
            "amount": formatted_amount,
            "currency": currency.upper(),
            "is_upgrade": is_upgrade,
            "effective_date": formatted_effective_date,
            "feature_changes": feature_changes,
            "billing_url": f"{settings.FRONTEND_URL}/billing"
        },
        recipient_name=user_name,
        categories=["billing", "subscription"]
    )

async def send_subscription_renewed_email(
    user_email: str,
    user_name: str,
    plan_name: str,
    renewal_date: datetime,
    next_billing_date: datetime,
    amount: float,
    currency: str,
    new_features: Optional[List[str]] = None
) -> bool:
    """
    Send an email when a subscription is renewed.

    Args:
        user_email: User's email address
        user_name: User's name
        plan_name: Name of the subscription plan
        renewal_date: Date when the subscription was renewed
        next_billing_date: Date of the next billing
        amount: Subscription amount
        currency: Currency code (e.g., USD)
        new_features: Optional list of new features to highlight

    Returns:
        True if email was sent successfully, False otherwise
    """
    # Format dates
    formatted_renewal_date = renewal_date.strftime("%B %d, %Y")
    formatted_next_billing_date = next_billing_date.strftime("%B %d, %Y")

    # Create the billing URL
    billing_url = f"{settings.FRONTEND_URL}/billing/subscription"

    return await email_service.send_email(
        recipient_email=user_email,
        subject=f"Your {plan_name} Subscription Has Been Renewed",
        template_name="subscription_renewed",
        template_data={
            "user_name": user_name,
            "plan_name": plan_name,
            "renewal_date": formatted_renewal_date,
            "next_billing_date": formatted_next_billing_date,
            "amount": amount,
            "currency": currency.upper(),
            "billing_url": billing_url,
            "new_features": new_features
        },
        recipient_name=user_name,
        categories=["billing", "subscription_renewed"]
    )

async def send_appsumo_redemption_confirmation_email(
    user_email: str,
    user_name: str,
    tier_name: str,
    tier_description: str,
    subscription_transition_data: dict
) -> bool:
    """
    Send a confirmation email when an AppSumo code is redeemed.

    Args:
        user_email: User's email address
        user_name: User's name
        tier_name: Name of the AppSumo tier
        tier_description: Description of the AppSumo tier
        subscription_transition_data: Data about the subscription transition

    Returns:
        True if email was sent successfully, False otherwise
    """
    # Get previous subscription info for the email
    previous_subscription = subscription_transition_data.get("previous_subscription")
    previous_plan = previous_subscription.get("plan_id", "Free") if previous_subscription else "Free"

    return await email_service.send_email(
        recipient_email=user_email,
        subject="🎉 AppSumo Lifetime Deal Activated - Welcome to Your New Plan!",
        template_name="appsumo_redemption_confirmation",
        template_data={
            "name": user_name,
            "tier_name": tier_name,
            "tier_description": tier_description,
            "previous_plan": previous_plan,
            "dashboard_url": f"{settings.FRONTEND_URL}/dashboard",
            "billing_url": f"{settings.FRONTEND_URL}/billing",
            "support_url": f"{settings.FRONTEND_URL}/support",
            "appsumo_code": subscription_transition_data.get("appsumo_code", ""),
            "redemption_date": subscription_transition_data.get("timestamp", datetime.now(timezone.utc)).strftime("%B %d, %Y"),
            "features_url": f"{settings.FRONTEND_URL}/features"
        },
        recipient_name=user_name,
        categories=["appsumo", "redemption", "lifetime_deal"]
    )

async def send_account_deletion_email(
    user_email: str,
    user_name: str,
    deletion_date: datetime
) -> bool:
    """
    Send an email when an account deletion is requested.

    Args:
        user_email: User's email address
        user_name: User's name
        deletion_date: Date when the account will be permanently deleted

    Returns:
        True if email was sent successfully, False otherwise
    """
    # Format the deletion date
    formatted_deletion_date = deletion_date.strftime("%B %d, %Y")

    # Create URLs
    cancellation_url = f"{settings.FRONTEND_URL}/settings/account/cancel-deletion"
    feedback_url = f"{settings.FRONTEND_URL}/feedback?type=account_deletion"
    export_url = f"{settings.FRONTEND_URL}/settings/account/export-data"

    return await email_service.send_email(
        recipient_email=user_email,
        subject="Account Deletion Confirmation",
        template_name="account_deletion",
        template_data={
            "user_name": user_name,
            "deletion_date": formatted_deletion_date,
            "cancellation_url": cancellation_url,
            "feedback_url": feedback_url,
            "export_url": export_url
        },
        recipient_name=user_name,
        categories=["account", "account_deletion"]
    )

async def send_rate_limit_email(
    user_email: str,
    user_name: str,
    feature_name: str,
    current_limit: int,
    current_usage: int,
    limit_period: str,
    reset_date: datetime,
    next_plan_name: Optional[str] = None,
    next_plan_limit: Optional[int] = None
) -> bool:
    """
    Send an email when a rate limit is reached.

    Args:
        user_email: User's email address
        user_name: User's name
        feature_name: Name of the feature that reached the limit
        current_limit: Current plan limit
        current_usage: Current usage
        limit_period: Period for the limit (e.g., "per day", "per month")
        reset_date: Date when the limit will reset
        next_plan_name: Optional name of the next higher plan
        next_plan_limit: Optional limit of the next higher plan

    Returns:
        True if email was sent successfully, False otherwise
    """
    # Format the reset date
    formatted_reset_date = reset_date.strftime("%B %d, %Y")

    # Create the upgrade URL
    upgrade_url = f"{settings.FRONTEND_URL}/billing/upgrade"

    # Calculate the difference between current and next plan limit
    limit_difference = None
    if next_plan_limit is not None and current_limit is not None:
        limit_difference = next_plan_limit - current_limit

    return await email_service.send_email(
        recipient_email=user_email,
        subject=f"Rate Limit Reached: {feature_name}",
        template_name="rate_limit",
        template_data={
            "user_name": user_name,
            "feature_name": feature_name,
            "current_limit": current_limit,
            "current_usage": current_usage,
            "limit_period": limit_period,
            "reset_date": formatted_reset_date,
            "upgrade_url": upgrade_url,
            "next_plan_name": next_plan_name,
            "next_plan_limit": next_plan_limit,
            "limit_difference": limit_difference
        },
        recipient_name=user_name,
        categories=["system", "rate_limit"]
    )

async def send_content_failed_email(
    user_email: str,
    user_name: str,
    content_title: str,
    platform: str,
    scheduled_time: str,
    content_id: str,
    error_message: str,
    error_type: Optional[str] = None,
    campaign_name: Optional[str] = None
) -> bool:
    """
    Send a notification when content publishing fails.

    Args:
        user_email: User's email address
        user_name: User's name
        content_title: Title of the content
        platform: Social media platform
        scheduled_time: Scheduled publication time
        content_id: ID of the content
        error_message: Error message
        error_type: Type of error (auth, rate_limit, content, general)
        campaign_name: Optional campaign name

    Returns:
        True if email was sent successfully, False otherwise
    """
    # Create the content URL
    content_url = f"{settings.FRONTEND_URL}/content/{content_id}"

    # Determine error description based on error type
    error_description = "An unexpected error occurred while trying to publish your content."
    if error_type == "auth":
        error_description = "There was an authentication issue with your social media account. Your account may need to be reconnected."
    elif error_type == "rate_limit":
        error_description = "The platform's rate limits have been reached. This usually happens when too many posts are published in a short time period."
    elif error_type == "content":
        error_description = "There was an issue with the content format or media files. The platform may have rejected the content."

    return await email_service.send_email(
        recipient_email=user_email,
        subject=f"Content Publishing Failed: {content_title}",
        template_name="content_failed",
        template_data={
            "user_name": user_name,
            "content_title": content_title,
            "platform": platform,
            "scheduled_time": scheduled_time,
            "campaign_name": campaign_name,
            "content_url": content_url,
            "error_message": error_message,
            "error_type": error_type,
            "error_description": error_description
        },
        recipient_name=user_name,
        categories=["content", "content_failed"]
    )
