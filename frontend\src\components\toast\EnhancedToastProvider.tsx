/**
 * Enhanced Toast Provider
 * Main component that provides the complete toast notification system
 */

import React from 'react';
import { EnhancedToastProvider as ContextProvider } from '../../contexts/EnhancedToastContext';
import ToastContainer from './ToastContainer';
import { ToastProviderProps, ToastPosition } from '../../types/toast';

interface EnhancedToastProviderProps extends ToastProviderProps {
  position?: ToastPosition;
  maxVisible?: number;
  stackSpacing?: number;
  enableKeyboardNavigation?: boolean;
  enableStacking?: boolean;
  enableNetworkRetry?: boolean;
  enableAccessibility?: boolean;
}

const EnhancedToastProvider: React.FC<EnhancedToastProviderProps> = ({
  children,
  options = {},
  position = 'bottom-right',
  maxVisible = 5,
  stackSpacing = 8,
  enableKeyboardNavigation = true,
  enableStacking = true,
  enableNetworkRetry = true,
  enableAccessibility = true,
}) => {
  // Enhanced options with defaults
  const enhancedOptions = {
    maxVisible,
    maxQueue: 20,
    stackSpacing,
    animationDuration: 300,
    enableStacking,
    enablePersistence: false,
    persistenceKey: 'aceo-toast-notifications',
    ...options,
  };

  return (
    <ContextProvider options={enhancedOptions} maxVisible={maxVisible}>
      {children}
      <ToastContainer
        position={position}
        maxVisible={maxVisible}
        stackSpacing={stackSpacing}
        enableKeyboardNavigation={enableKeyboardNavigation}
        enableStacking={enableStacking}
      />
    </ContextProvider>
  );
};

export default EnhancedToastProvider;
