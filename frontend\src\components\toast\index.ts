/**
 * Toast Notification System Exports
 * Production-ready toast notification system for ACE Social platform
 */

// Main components
export { default as EnhancedToastProvider } from './EnhancedToastProvider';
export { default as ProductionToast } from './ProductionToast';
export { default as ToastContainer } from './ToastContainer';

// Context and hooks
export { EnhancedToastProvider as EnhancedToastContextProvider, useEnhancedToast } from '../../contexts/EnhancedToastContext';
export { useAdvancedToast } from '../../hooks/useAdvancedToast';
export { useToastAccessibility } from '../../hooks/useToastAccessibility';

// Types
export type {
  ToastType,
  ToastPosition,
  ToastPriority,
  ToastAction,
  ToastConfig,
  ToastState,
  ToastQueueOptions,
  ToastContextValue,
  ToastProviderProps,
  ToastError,
  NetworkFailureConfig,
  AccessibilityConfig,
  AnimationConfig,
  ToastTheme,
} from '../../types/toast';

// Utilities
export {
  ToastRateLimiter,
  NetworkFailureHandler,
  ToastDeduplicator,
  ConcurrentNotificationManager,
  ToastPersistenceManager,
  createToastId,
  sanitizeToastMessage,
  getToastDuration,
  isNetworkError,
} from '../../utils/toastUtils';

// Default configurations
export const DEFAULT_TOAST_CONFIG = {
  position: 'bottom-right' as const,
  maxVisible: 5,
  enableStacking: true,
  enableKeyboardNavigation: true,
  enableAccessibility: true,
  animationDuration: 300,
};

export const DEFAULT_DURATIONS = {
  success: 4000,
  info: 5000,
  warning: 6000,
  error: 8000,
};

export const DEFAULT_NETWORK_CONFIG = {
  enableRetry: true,
  maxRetries: 3,
  retryDelay: 1000,
  exponentialBackoff: true,
};

// Re-export for backward compatibility (optional)
export { useNotification } from '../../hooks/useNotification';
export { NotificationProvider } from '../../contexts/NotificationContext';
