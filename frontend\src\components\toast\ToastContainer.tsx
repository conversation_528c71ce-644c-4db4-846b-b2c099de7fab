/**
 * Toast Container Component
 * Manages positioning, stacking, and rendering of toast notifications
 */

import React, { useEffect, useState, useCallback } from 'react';
import { Box, Portal, useTheme, useMediaQuery, Typography, alpha } from '@mui/material';
import { TransitionGroup } from 'react-transition-group';
import ProductionToast from './ProductionToast';
import { useEnhancedToast } from '../../contexts/EnhancedToastContext';
import { ToastPosition, ToastState } from '../../types/toast';

interface ToastContainerProps {
  position?: ToastPosition;
  maxVisible?: number;
  stackSpacing?: number;
  enableKeyboardNavigation?: boolean;
  enableStacking?: boolean;
}

const ToastContainer: React.FC<ToastContainerProps> = ({
  position = 'bottom-right',
  maxVisible = 5,
  stackSpacing = 8,
  enableKeyboardNavigation = true,
  enableStacking = true,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const {
    toasts,
    dismissToast,
    updateToast,
    options,
  } = useEnhancedToast();

  const [visibleToasts, setVisibleToasts] = useState<ToastState[]>([]);
  const [containerDimensions, setContainerDimensions] = useState({ width: 0, height: 0 });

  // Filter and sort toasts for display
  useEffect(() => {
    const filtered = toasts
      .filter(toast => toast.isVisible && !toast.isExiting)
      .sort((a, b) => {
        // Sort by priority first, then by creation time
        const priorityOrder = { critical: 4, high: 3, normal: 2, low: 1 };
        const aPriority = priorityOrder[a.config.priority || 'normal'];
        const bPriority = priorityOrder[b.config.priority || 'normal'];
        
        if (aPriority !== bPriority) {
          return bPriority - aPriority; // Higher priority first
        }
        
        return b.createdAt.getTime() - a.createdAt.getTime(); // Newer first
      })
      .slice(0, maxVisible);

    setVisibleToasts(filtered);
  }, [toasts, maxVisible]);

  // Get container position styles
  const getContainerPosition = () => {
    const spacing = isMobile ? 16 : 24;
    const mobileAdjustment = isMobile ? { left: spacing, right: spacing, width: `calc(100% - ${spacing * 2}px)` } : {};

    switch (position) {
      case 'top-left':
        return {
          top: spacing,
          left: spacing,
          ...mobileAdjustment,
        };
      case 'top-center':
        return {
          top: spacing,
          left: '50%',
          transform: 'translateX(-50%)',
          ...mobileAdjustment,
        };
      case 'top-right':
        return {
          top: spacing,
          right: spacing,
          ...mobileAdjustment,
        };
      case 'bottom-left':
        return {
          bottom: spacing,
          left: spacing,
          ...mobileAdjustment,
        };
      case 'bottom-center':
        return {
          bottom: spacing,
          left: '50%',
          transform: 'translateX(-50%)',
          ...mobileAdjustment,
        };
      case 'bottom-right':
      default:
        return {
          bottom: spacing,
          right: spacing,
          ...mobileAdjustment,
        };
    }
  };

  // Handle toast actions
  const handleToastAction = useCallback((toastId: string) => {
    const toast = toasts.find(t => t.id === toastId);
    if (toast?.config.onAction) {
      toast.config.onAction();
    }
    dismissToast(toastId);
  }, [toasts, dismissToast]);

  const handleToastUndo = useCallback((toastId: string) => {
    const toast = toasts.find(t => t.id === toastId);
    if (toast?.config.onUndo) {
      toast.config.onUndo();
    }
    dismissToast(toastId);
  }, [toasts, dismissToast]);

  // Calculate stacking positions
  const getStackedToasts = () => {
    if (!enableStacking) {
      return visibleToasts.map((toast, index) => ({
        toast,
        stackIndex: 0,
        offset: index * (stackSpacing + 80), // Approximate toast height + spacing
      }));
    }

    return visibleToasts.map((toast, index) => ({
      toast,
      stackIndex: index,
      offset: 0, // Stacking is handled in the toast component
    }));
  };

  // Handle container resize for responsive positioning
  useEffect(() => {
    const updateDimensions = () => {
      setContainerDimensions({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  // Keyboard navigation for container
  const handleContainerKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (!enableKeyboardNavigation || visibleToasts.length === 0) return;

    switch (event.key) {
      case 'Escape':
        // Dismiss all toasts
        event.preventDefault();
        visibleToasts.forEach(toast => dismissToast(toast.id));
        break;
      case 'ArrowUp':
      case 'ArrowDown':
        // Navigate between toasts (could be implemented for focus management)
        event.preventDefault();
        break;
    }
  }, [enableKeyboardNavigation, visibleToasts, dismissToast]);

  // Don't render if no toasts
  if (visibleToasts.length === 0) {
    return null;
  }

  const stackedToasts = getStackedToasts();

  return (
    <Portal>
      <Box
        role="region"
        aria-label="Notifications"
        aria-live="polite"
        onKeyDown={handleContainerKeyDown}
        sx={{
          position: 'fixed',
          zIndex: theme.zIndex.snackbar,
          pointerEvents: 'none',
          ...getContainerPosition(),
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: position.includes('top') ? 'column' : 'column-reverse',
            gap: enableStacking ? 0 : `${stackSpacing}px`,
            alignItems: position.includes('center') ? 'center' : 'stretch',
            maxHeight: '100vh',
            overflow: 'hidden',
          }}
        >
          <TransitionGroup component={null}>
            {stackedToasts.map(({ toast, stackIndex, offset }) => (
              <Box
                key={toast.id}
                sx={{
                  pointerEvents: 'auto',
                  marginBottom: enableStacking ? 0 : undefined,
                  transform: !enableStacking ? `translateY(${offset}px)` : undefined,
                  transition: theme.transitions.create('transform', {
                    duration: options.animationDuration || 300,
                    easing: theme.transitions.easing.easeInOut,
                  }),
                }}
              >
                <ProductionToast
                  toast={toast}
                  onDismiss={dismissToast}
                  onAction={handleToastAction}
                  onUndo={handleToastUndo}
                  position={position}
                  stackIndex={stackIndex}
                  animationDuration={options.animationDuration}
                  enableKeyboardNavigation={enableKeyboardNavigation}
                />
              </Box>
            ))}
          </TransitionGroup>
        </Box>

        {/* Queue indicator for mobile */}
        {isMobile && toasts.length > maxVisible && (
          <Box
            sx={{
              position: 'absolute',
              bottom: position.includes('bottom') ? '100%' : 'auto',
              top: position.includes('top') ? '100%' : 'auto',
              left: '50%',
              transform: 'translateX(-50%)',
              mt: position.includes('top') ? 1 : 0,
              mb: position.includes('bottom') ? 1 : 0,
              px: 2,
              py: 0.5,
              backgroundColor: alpha(theme.palette.background.paper, 0.9),
              borderRadius: 1,
              backdropFilter: 'blur(8px)',
              pointerEvents: 'auto',
            }}
          >
            <Typography
              variant="caption"
              sx={{
                color: theme.palette.text.secondary,
                fontSize: '0.7rem',
              }}
            >
              +{toasts.length - maxVisible} more notifications
            </Typography>
          </Box>
        )}
      </Box>
    </Portal>
  );
};

export default ToastContainer;
