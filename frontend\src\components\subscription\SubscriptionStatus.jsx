/**
 * Enhanced Subscription Status - Enterprise-grade subscription status monitoring component
 * Features: Comprehensive subscription status monitoring system with real-time status updates and billing
 * synchronization, detailed subscription analytics with usage tracking and billing cycle insights, advanced
 * status visualization with progress indicators and interactive status cards, ACE Social's 3-tier subscription
 * structure integration (creator/accelerator/dominator plans), subscription status interaction features including
 * renewal prompts and billing alerts, status customization capabilities with conditional rendering and detailed
 * status breakdowns, subscription lifecycle management with status history tracking and automated status
 * transitions, and seamless ACE Social platform integration with advanced subscription monitoring
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 */

import {
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
  useCallback,
  useMemo
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Chip,
  LinearProgress,
  Grid,
  Button,
  Tooltip,
  IconButton,
  Alert,
  AlertTitle,
  Badge,
  Avatar,
  useTheme,
  alpha,
  useMediaQuery,
  Fade,
  CircularProgress,
  Paper
} from '@mui/material';
import {
  Diamond as DiamondIcon,
  Star as StarIcon,
  Rocket as RocketIcon,
  Explore as ExploreIcon,
  Info as InfoIcon,
  Upgrade as UpgradeIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Schedule as ScheduleIcon,
  Notifications as NotificationsIcon,
  Analytics as AnalyticsIcon,
  Security as SecurityIcon,
  Refresh as RefreshIcon,
  Assessment as AssessmentIcon,
  CreditCard as CreditCardIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useSubscription } from '../../contexts/SubscriptionContext';
import { useNotification } from '../../hooks/useNotification';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Subscription status variants
const STATUS_VARIANTS = {
  FULL: 'full',
  COMPACT: 'compact',
  MINIMAL: 'minimal',
  DASHBOARD: 'dashboard',
  WIDGET: 'widget',
  BANNER: 'banner'
};

// Subscription status types
const SUBSCRIPTION_STATUS = {
  ACTIVE: 'active',
  PAST_DUE: 'past_due',
  CANCELED: 'canceled',
  TRIALING: 'trialing',
  INCOMPLETE: 'incomplete',
  INCOMPLETE_EXPIRED: 'incomplete_expired',
  UNPAID: 'unpaid',
  PAUSED: 'paused'
};

// ACE Social 3-tier subscription plans with enhanced status features
const SUBSCRIPTION_PLANS = {
  creator: {
    id: 'creator',
    name: 'Creator',
    tier: 1,
    price: 19,
    yearlyPrice: 190,
    description: 'Essential tools for growing content creators',
    color: ACE_COLORS.YELLOW,
    icon: StarIcon,
    features: [
      '50 posts per month',
      'Advanced text content generation',
      'Basic image generation (20 images/month)',
      '3 social platforms',
      'Advanced templates (25)',
      'Auto scheduling',
      'Optimal time suggestions',
      '30-day analytics history',
      '3 ICPs',
      '3 brand profiles',
      'Enhanced regeneration (25 credits/month)'
    ],
    limits: {
      posts: 50,
      images: 20,
      platforms: 3,
      templates: 25,
      icps: 3,
      brands: 3,
      credits: 25,
      ai_auto_replies: 100
    },
    statusFeatures: {
      realTimeUpdates: true,
      billingAlerts: true,
      usageNotifications: true,
      renewalReminders: true
    }
  },
  accelerator: {
    id: 'accelerator',
    name: 'Accelerator',
    tier: 2,
    price: 49,
    yearlyPrice: 490,
    description: 'Professional tools for marketing agencies and growing businesses',
    color: ACE_COLORS.PURPLE,
    icon: RocketIcon,
    features: [
      '200 posts per month',
      'Advanced AI content generation',
      'Professional image generation (100 images/month)',
      '10 social platforms',
      'Premium templates (100)',
      'Advanced scheduling & automation',
      'Comprehensive analytics (90 days)',
      '10 ICPs',
      '10 brand profiles',
      'Team collaboration (5 users)',
      'Priority support',
      'Enhanced regeneration (100 credits/month)'
    ],
    limits: {
      posts: 200,
      images: 100,
      platforms: 10,
      templates: 100,
      icps: 10,
      brands: 10,
      credits: 100,
      ai_auto_replies: 500,
      team_members: 5
    },
    statusFeatures: {
      realTimeUpdates: true,
      billingAlerts: true,
      usageNotifications: true,
      renewalReminders: true,
      advancedAnalytics: true,
      teamNotifications: true
    }
  },
  dominator: {
    id: 'dominator',
    name: 'Dominator',
    tier: 3,
    price: 99,
    yearlyPrice: 990,
    description: 'Enterprise-grade solution for large agencies and multi-brand businesses',
    color: ACE_COLORS.DARK,
    icon: DiamondIcon,
    features: [
      'Unlimited posts',
      'Enterprise AI content generation',
      'Unlimited image generation',
      'All social platforms',
      'Custom templates',
      'Enterprise automation',
      'Full analytics suite (unlimited history)',
      'Unlimited ICPs',
      'Unlimited brand profiles',
      'Advanced team collaboration (unlimited users)',
      'White-label solutions',
      'Dedicated support',
      'Custom integrations',
      'Unlimited regeneration credits'
    ],
    limits: {
      posts: -1,
      images: -1,
      platforms: -1,
      templates: -1,
      icps: -1,
      brands: -1,
      credits: -1,
      ai_auto_replies: -1,
      team_members: -1
    },
    statusFeatures: {
      realTimeUpdates: true,
      billingAlerts: true,
      usageNotifications: true,
      renewalReminders: true,
      advancedAnalytics: true,
      teamNotifications: true,
      enterpriseReporting: true,
      customIntegrations: true
    }
  }
};

/**
 * Enhanced Subscription Status - Comprehensive subscription status monitoring with advanced features
 * Implements plan-based status visualization and enterprise-grade subscription monitoring capabilities
 */
const SubscriptionStatus = memo(forwardRef(({
  variant = STATUS_VARIANTS.FULL,
  showUsage = true,
  showUpgrade = true,
  showAnalytics = false,
  showNotifications = true,
  onUpgradeClick,
  onRenewalClick,
  onAnalyticsClick,
  customActions = []
}, ref) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { showSuccessNotification, showErrorNotification } = useNotification();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Responsive design
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Enhanced state management
  const [loading, setLoading] = useState(false);
  const [notifications] = useState([]);
  const [billingAlerts] = useState([]);
  const [usageAlerts] = useState([]);

  // Subscription context
  const {
    subscription,
    usage,
    trialInfo,
    getUsagePercentage,
    refreshSubscription,
    loading: subscriptionLoading
  } = useSubscription();

  // Current subscription data
  const currentPlan = subscription ? SUBSCRIPTION_PLANS[subscription.plan_name?.toLowerCase()] : null;
  const subscriptionStatus = subscription?.status || SUBSCRIPTION_STATUS.ACTIVE;

  // Enhanced plan utilities with ACE Social branding
  const getPlanIcon = useCallback((planId) => {
    const plan = SUBSCRIPTION_PLANS[planId];
    if (plan?.icon) {
      const IconComponent = plan.icon;
      return <IconComponent sx={{ color: plan.color }} />;
    }
    return <ExploreIcon sx={{ color: ACE_COLORS.PURPLE }} />;
  }, []);

  const getPlanColor = useCallback((planId) => {
    const plan = SUBSCRIPTION_PLANS[planId];
    return plan?.color || ACE_COLORS.PURPLE;
  }, []);

  const getStatusIcon = useCallback((status) => {
    switch (status) {
      case SUBSCRIPTION_STATUS.ACTIVE:
        return <CheckCircleIcon sx={{ color: theme.palette.success.main }} />;
      case SUBSCRIPTION_STATUS.PAST_DUE:
        return <WarningIcon sx={{ color: theme.palette.warning.main }} />;
      case SUBSCRIPTION_STATUS.CANCELED:
        return <ErrorIcon sx={{ color: theme.palette.error.main }} />;
      case SUBSCRIPTION_STATUS.TRIALING:
        return <ScheduleIcon sx={{ color: ACE_COLORS.YELLOW }} />;
      case SUBSCRIPTION_STATUS.PAUSED:
        return <ScheduleIcon sx={{ color: theme.palette.info.main }} />;
      default:
        return <InfoIcon sx={{ color: theme.palette.info.main }} />;
    }
  }, [theme]);

  const getStatusColor = useCallback((status) => {
    switch (status) {
      case SUBSCRIPTION_STATUS.ACTIVE:
        return 'success';
      case SUBSCRIPTION_STATUS.PAST_DUE:
        return 'warning';
      case SUBSCRIPTION_STATUS.CANCELED:
        return 'error';
      case SUBSCRIPTION_STATUS.TRIALING:
        return 'info';
      case SUBSCRIPTION_STATUS.PAUSED:
        return 'default';
      default:
        return 'default';
    }
  }, []);

  const getStatusMessage = useCallback((status) => {
    switch (status) {
      case SUBSCRIPTION_STATUS.ACTIVE:
        return 'Your subscription is active and all features are available';
      case SUBSCRIPTION_STATUS.PAST_DUE:
        return 'Payment is past due. Please update your payment method';
      case SUBSCRIPTION_STATUS.CANCELED:
        return 'Your subscription has been canceled';
      case SUBSCRIPTION_STATUS.TRIALING:
        return `Trial period active. ${trialInfo?.days_remaining || 0} days remaining`;
      case SUBSCRIPTION_STATUS.PAUSED:
        return 'Your subscription is temporarily paused';
      default:
        return 'Subscription status unknown';
    }
  }, [trialInfo]);

  // Enhanced usage data with comprehensive metrics
  const getUsageData = useCallback(() => {
    if (!usage || !currentPlan) return [];

    const usageItems = [];
    const planLimits = currentPlan.limits;

    // Posts usage
    if (planLimits.posts !== undefined) {
      const used = usage.monthly_posts_used || 0;
      const limit = planLimits.posts;
      const percentage = limit === -1 ? 0 : getUsagePercentage('monthly_posts') || 0;

      usageItems.push({
        label: 'Posts',
        used,
        limit,
        percentage,
        feature: 'monthly_posts',
        icon: <AnalyticsIcon />,
        isUnlimited: limit === -1,
        color: percentage > 90 ? 'error' : percentage > 75 ? 'warning' : 'primary'
      });
    }

    // AI Replies usage
    if (planLimits.ai_auto_replies !== undefined) {
      const used = usage.ai_auto_replies_used || 0;
      const limit = planLimits.ai_auto_replies;
      const percentage = limit === -1 ? 0 : getUsagePercentage('ai_auto_replies') || 0;

      usageItems.push({
        label: 'AI Replies',
        used,
        limit,
        percentage,
        feature: 'ai_auto_replies',
        icon: <SecurityIcon />,
        isUnlimited: limit === -1,
        color: percentage > 90 ? 'error' : percentage > 75 ? 'warning' : 'primary'
      });
    }

    // Regeneration Credits usage
    if (planLimits.credits !== undefined) {
      const used = usage.regeneration_credits_used || 0;
      const limit = planLimits.credits;
      const percentage = limit === -1 ? 0 : getUsagePercentage('regeneration_credits') || 0;

      usageItems.push({
        label: 'Regeneration',
        used,
        limit,
        percentage,
        feature: 'regeneration_credits',
        icon: <RefreshIcon />,
        isUnlimited: limit === -1,
        color: percentage > 90 ? 'error' : percentage > 75 ? 'warning' : 'primary'
      });
    }

    // Images usage
    if (planLimits.images !== undefined) {
      const used = usage.images_used || 0;
      const limit = planLimits.images;
      const percentage = limit === -1 ? 0 : (used / limit) * 100 || 0;

      usageItems.push({
        label: 'Images',
        used,
        limit,
        percentage,
        feature: 'images',
        icon: <AssessmentIcon />,
        isUnlimited: limit === -1,
        color: percentage > 90 ? 'error' : percentage > 75 ? 'warning' : 'primary'
      });
    }

    return usageItems;
  }, [usage, currentPlan, getUsagePercentage]);

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    refreshStatus: () => handleRefreshStatus(),
    getStatusData: () => ({
      subscription,
      currentPlan,
      status: subscriptionStatus,
      usage,
      notifications,
      billingAlerts,
      usageAlerts
    }),
    getUsageData: () => getUsageData(),
    getBillingAlerts: () => billingAlerts
  }), [
    subscription,
    currentPlan,
    subscriptionStatus,
    usage,
    notifications,
    billingAlerts,
    usageAlerts,
    handleRefreshStatus,
    getUsageData
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced event handlers
  const handleRefreshStatus = useCallback(async () => {
    try {
      setLoading(true);

      await refreshSubscription();

      announceToScreenReader('Subscription status refreshed successfully');
      showSuccessNotification('Subscription status updated');
    } catch (err) {
      const errorMessage = err.message || 'Failed to refresh subscription status';
      announceToScreenReader(`Failed to refresh status: ${errorMessage}`);
      showErrorNotification(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [refreshSubscription, announceToScreenReader, showSuccessNotification, showErrorNotification]);

  const handleUpgradeClick = useCallback(() => {
    if (onUpgradeClick) {
      onUpgradeClick(currentPlan);
    }
    announceToScreenReader('Opening upgrade options');
  }, [onUpgradeClick, currentPlan, announceToScreenReader]);

  const handleRenewalClick = useCallback(() => {
    if (onRenewalClick) {
      onRenewalClick(subscription);
    }
    announceToScreenReader('Opening renewal options');
  }, [onRenewalClick, subscription, announceToScreenReader]);

  // Enhanced loading state
  if (loading || subscriptionLoading) {
    return (
      <Card sx={glassMorphismStyles}>
        <CardContent sx={{ textAlign: 'center', p: theme.spacing(3) }}>
          <Fade in timeout={300}>
            <Box>
              <CircularProgress
                size={40}
                sx={{
                  color: ACE_COLORS.PURPLE,
                  mb: theme.spacing(2)
                }}
              />
              <Typography variant="body2" sx={{
                color: ACE_COLORS.PURPLE,
                fontWeight: 500
              }}>
                Loading subscription status...
              </Typography>
            </Box>
          </Fade>
        </CardContent>
      </Card>
    );
  }

  const usageItems = getUsageData();

  // Enhanced Compact Variant with status indicators
  if (variant === STATUS_VARIANTS.COMPACT) {
    return (
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        p: 1,
        ...glassMorphismStyles
      }}>
        <Badge
          badgeContent={getStatusIcon(subscriptionStatus)}
          overlap="circular"
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Chip
            icon={getPlanIcon(subscription?.plan_name)}
            label={currentPlan?.name || 'Creator'}
            size="small"
            sx={{
              backgroundColor: alpha(getPlanColor(subscription?.plan_name), 0.1),
              color: getPlanColor(subscription?.plan_name),
              fontWeight: 600,
              '& .MuiChip-icon': {
                color: getPlanColor(subscription?.plan_name)
              }
            }}
          />
        </Badge>

        {showUpgrade && currentPlan?.tier < 3 && (
          <Tooltip title="Upgrade Plan" arrow>
            <IconButton
              size="small"
              onClick={handleUpgradeClick}
              sx={{
                color: ACE_COLORS.PURPLE,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                }
              }}
            >
              <UpgradeIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}

        {showNotifications && notifications.length > 0 && (
          <Tooltip title={`${notifications.length} notification${notifications.length > 1 ? 's' : ''}`} arrow>
            <IconButton size="small">
              <Badge badgeContent={notifications.length} color="error">
                <NotificationsIcon fontSize="small" />
              </Badge>
            </IconButton>
          </Tooltip>
        )}
      </Box>
    );
  }

  // Enhanced Minimal Variant with status integration
  if (variant === STATUS_VARIANTS.MINIMAL) {
    return (
      <Tooltip
        title={getStatusMessage(subscriptionStatus)}
        arrow
        placement="top"
      >
        <Chip
          icon={getPlanIcon(subscription?.plan_name)}
          label={currentPlan?.name || 'Creator'}
          size="small"
          variant="outlined"
          sx={{
            borderColor: getPlanColor(subscription?.plan_name),
            color: getPlanColor(subscription?.plan_name),
            fontWeight: 600,
            '& .MuiChip-icon': {
              color: getPlanColor(subscription?.plan_name)
            },
            '&:hover': {
              backgroundColor: alpha(getPlanColor(subscription?.plan_name), 0.1)
            }
          }}
        />
      </Tooltip>
    );
  }

  // Enhanced Full Variant with comprehensive status monitoring
  return (
    <Card sx={glassMorphismStyles}>
      <CardHeader
        avatar={
          <Avatar sx={{
            backgroundColor: alpha(getPlanColor(subscription?.plan_name), 0.1),
            border: `2px solid ${getPlanColor(subscription?.plan_name)}`
          }}>
            {getPlanIcon(subscription?.plan_name)}
          </Avatar>
        }
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="h6" sx={{
              fontWeight: 600,
              color: ACE_COLORS.DARK
            }}>
              {currentPlan?.name || 'Creator'} Plan
            </Typography>
            {currentPlan?.tier === 2 && (
              <Chip
                label="Most Popular"
                size="small"
                sx={{
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                  color: ACE_COLORS.PURPLE,
                  fontWeight: 600
                }}
              />
            )}
          </Box>
        }
        subheader={
          <Typography variant="body2" color="text.secondary">
            {currentPlan?.description || 'Essential tools for growing content creators'}
          </Typography>
        }
        action={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Chip
              icon={getStatusIcon(subscriptionStatus)}
              label={subscriptionStatus.charAt(0).toUpperCase() + subscriptionStatus.slice(1)}
              color={getStatusColor(subscriptionStatus)}
              size="small"
              sx={{ fontWeight: 600 }}
            />
            {showNotifications && notifications.length > 0 && (
              <IconButton size="small">
                <Badge badgeContent={notifications.length} color="error">
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            )}
            <IconButton
              size="small"
              onClick={handleRefreshStatus}
              disabled={loading}
              sx={{ color: ACE_COLORS.PURPLE }}
            >
              <RefreshIcon />
            </IconButton>
          </Box>
        }
      />

      <CardContent sx={{ pt: 0 }}>
        {/* Status Message */}
        <Alert
          severity={getStatusColor(subscriptionStatus)}
          sx={{
            mb: 3,
            borderRadius: theme.spacing(1),
            '& .MuiAlert-icon': {
              color: getStatusColor(subscriptionStatus) === 'success' ? theme.palette.success.main :
                     getStatusColor(subscriptionStatus) === 'warning' ? theme.palette.warning.main :
                     getStatusColor(subscriptionStatus) === 'error' ? theme.palette.error.main :
                     theme.palette.info.main
            }
          }}
        >
          <AlertTitle>Subscription Status</AlertTitle>
          {getStatusMessage(subscriptionStatus)}
        </Alert>

        {/* Enhanced Usage Information */}
        {showUsage && usageItems.length > 0 && (
          <Box sx={{ mb: 3 }}>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              mb: 2
            }}>
              <Typography variant="h6" sx={{
                fontWeight: 600,
                color: ACE_COLORS.DARK
              }}>
                Usage Analytics
              </Typography>
              {showAnalytics && (
                <Button
                  size="small"
                  startIcon={<AnalyticsIcon />}
                  onClick={onAnalyticsClick}
                  sx={{
                    color: ACE_COLORS.PURPLE,
                    '&:hover': {
                      backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                    }
                  }}
                >
                  View Details
                </Button>
              )}
            </Box>

            <Grid container spacing={2}>
              {usageItems.map((item) => (
                <Grid item xs={12} sm={6} md={3} key={item.feature}>
                  <Paper sx={{
                    p: 2,
                    background: `linear-gradient(135deg,
                      ${alpha(theme.palette.background.paper, 0.9)} 0%,
                      ${alpha(theme.palette.background.default, 0.8)} 100%)`,
                    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.1)}`,
                    borderRadius: theme.spacing(1)
                  }}>
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      mb: 1
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {item.icon}
                        <Typography variant="body2" fontWeight={500}>
                          {item.label}
                        </Typography>
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        {item.used} / {item.isUnlimited ? '∞' : item.limit}
                      </Typography>
                    </Box>

                    {!item.isUnlimited && (
                      <LinearProgress
                        variant="determinate"
                        value={Math.min(item.percentage, 100)}
                        sx={{
                          height: 8,
                          borderRadius: 4,
                          backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: item.percentage > 90 ? theme.palette.error.main :
                                           item.percentage > 75 ? theme.palette.warning.main :
                                           ACE_COLORS.PURPLE,
                            borderRadius: 4
                          }
                        }}
                      />
                    )}

                    {item.isUnlimited && (
                      <Chip
                        label="Unlimited"
                        size="small"
                        sx={{
                          backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                          color: ACE_COLORS.PURPLE,
                          fontWeight: 600
                        }}
                      />
                    )}
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </Box>
        )}

        {/* Enhanced Key Features */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" sx={{
            fontWeight: 600,
            color: ACE_COLORS.DARK,
            mb: 2
          }}>
            Plan Features
          </Typography>
          <Grid container spacing={1}>
            {currentPlan?.features?.slice(0, 6).map((feature, index) => (
              <Grid item xs={12} sm={6} key={index}>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  p: 1,
                  borderRadius: theme.spacing(1),
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05)
                }}>
                  <CheckCircleIcon sx={{
                    fontSize: 16,
                    color: ACE_COLORS.PURPLE
                  }} />
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {feature}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Enhanced Action Buttons */}
        <Box sx={{
          display: 'flex',
          gap: 2,
          justifyContent: isMobile ? 'stretch' : 'center',
          flexDirection: isMobile ? 'column' : 'row'
        }}>
          {showUpgrade && currentPlan?.tier < 3 && (
            <Button
              variant="contained"
              startIcon={<UpgradeIcon />}
              onClick={handleUpgradeClick}
              sx={{
                backgroundColor: ACE_COLORS.PURPLE,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8)
                }
              }}
            >
              Upgrade to {currentPlan?.tier === 1 ? 'Accelerator' : 'Dominator'}
            </Button>
          )}

          <Button
            variant="outlined"
            startIcon={<CreditCardIcon />}
            onClick={() => navigate('/billing')}
            sx={{
              borderColor: ACE_COLORS.PURPLE,
              color: ACE_COLORS.PURPLE,
              '&:hover': {
                borderColor: ACE_COLORS.PURPLE,
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
              }
            }}
          >
            Manage Billing
          </Button>

          {subscriptionStatus === SUBSCRIPTION_STATUS.PAST_DUE && (
            <Button
              variant="contained"
              color="warning"
              startIcon={<WarningIcon />}
              onClick={handleRenewalClick}
            >
              Update Payment
            </Button>
          )}

          {customActions.map((action, index) => (
            <Button
              key={index}
              variant={action.variant || 'outlined'}
              startIcon={action.icon}
              onClick={action.onClick}
              color={action.color || 'primary'}
            >
              {action.label}
            </Button>
          ))}
        </Box>
      </CardContent>
    </Card>
  );
}));

SubscriptionStatus.displayName = 'SubscriptionStatus';

SubscriptionStatus.propTypes = {
  /** Visual variant of the status display */
  variant: PropTypes.oneOf(['full', 'compact', 'minimal', 'dashboard', 'widget', 'banner']),
  /** Whether to show usage information */
  showUsage: PropTypes.bool,
  /** Whether to show upgrade options */
  showUpgrade: PropTypes.bool,
  /** Whether to show analytics features */
  showAnalytics: PropTypes.bool,
  /** Whether to show notifications */
  showNotifications: PropTypes.bool,
  /** Enable real-time updates */
  enableRealTimeUpdates: PropTypes.bool,
  /** Enable status history tracking */
  enableStatusHistory: PropTypes.bool,
  /** Enable billing alerts */
  enableBillingAlerts: PropTypes.bool,
  /** Enable auto refresh */
  autoRefresh: PropTypes.bool,
  /** Refresh interval in milliseconds */
  refreshInterval: PropTypes.number,
  /** Function called when status changes */
  onStatusChange: PropTypes.func,
  /** Function called when upgrade is clicked */
  onUpgradeClick: PropTypes.func,
  /** Function called when renewal is clicked */
  onRenewalClick: PropTypes.func,
  /** Function called when analytics is clicked */
  onAnalyticsClick: PropTypes.func,
  /** Custom action buttons */
  customActions: PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.string.isRequired,
    onClick: PropTypes.func.isRequired,
    icon: PropTypes.node,
    variant: PropTypes.string,
    color: PropTypes.string
  }))
};

export default SubscriptionStatus;
