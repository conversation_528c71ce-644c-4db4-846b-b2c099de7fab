/**
 * Enhanced Team Members List - Enterprise-grade team member management component
 * Features: Comprehensive team members list system with advanced filtering, sorting, and search capabilities
 * for member management, detailed member categorization with role management and permission levels, advanced
 * list features with bulk operations and multi-select functionality, ACE Social's team management system
 * integration with seamless member workflow and role assignment, member interaction features including quick
 * actions and inline editing, list customization capabilities with view preferences and column configuration,
 * real-time member updates with live status changes and activity tracking, and seamless ACE Social platform
 * integration with advanced member list orchestration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 */

import {
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
  useCallback,
  useMemo
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  Avatar,
  IconButton,
  Chip,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Select,
  FormControl,
  InputLabel,
  useTheme,
  Tooltip,
  alpha,
  Card,
  Stack,
  Badge,
  InputAdornment,
  Checkbox,
  FormControlLabel,
  LinearProgress,
  Fade,
  Zoom,
  Snackbar,
  Alert
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  AdminPanelSettings as AdminPanelSettingsIcon,
  Person as PersonIcon,
  Visibility as VisibilityIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Star as StarIcon,
  Email as EmailIcon,
  AccessTime as TimeIcon,
  Circle as CircleIcon
} from '@mui/icons-material';
import { useTeam } from '../../contexts/TeamContext';
import { useAuth } from '../../hooks/useAuth';
import { useConfirmation } from '../../contexts/ConfirmationContext';
import { formatDistanceToNow } from 'date-fns';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Team roles
const TEAM_ROLES = {
  OWNER: 'owner',
  ADMIN: 'admin',
  MEMBER: 'member',
  VIEWER: 'viewer'
};

// Sort options
const SORT_OPTIONS = {
  NAME: 'name',
  ROLE: 'role',
  JOINED: 'joined',
  ACTIVITY: 'activity'
};

// View modes
const VIEW_MODES = {
  LIST: 'list',
  CARDS: 'cards',
  COMPACT: 'compact'
};

// Member status
const MEMBER_STATUS = {
  ONLINE: 'online',
  OFFLINE: 'offline',
  AWAY: 'away',
  BUSY: 'busy'
};

/**
 * Enhanced Team Members List - Comprehensive member management with advanced features
 * Implements detailed member tracking and enterprise-grade list management capabilities
 */
const TeamMembersList = memo(forwardRef(({
  team,
  isOwnerOrAdmin = false,
  onTeamUpdated,
  onMemberAction,
  onAnalyticsTrack,
  enableBulkOperations = true,
  enableAdvancedFilters = true,
  maxMembersPerPage = 20,
  showOnlineStatus = true
}, ref) => {
  const theme = useTheme();
  const { user } = useAuth();
  const { removeTeamMember, updateTeamMemberRole } = useTeam();
  const { showConfirmation } = useConfirmation();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Enhanced state management
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const [selectedMember, setSelectedMember] = useState(null);
  const [selectedMembers, setSelectedMembers] = useState(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [filterRole, setFilterRole] = useState('all');
  const [sortBy, setSortBy] = useState(SORT_OPTIONS.NAME);
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [notifications, setNotifications] = useState([]);
  const [showMemberDialog, setShowMemberDialog] = useState(false);
  const [editingMember, setEditingMember] = useState(null);
  const [memberAnalytics, setMemberAnalytics] = useState({
    totalMembers: 0,
    activeMembers: 0,
    onlineMembers: 0,
    lastActivity: new Date().toISOString()
  });

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    refreshMembers: () => handleRefreshMembers(),
    selectAllMembers: () => handleSelectAll(),
    clearSelection: () => setSelectedMembers(new Set()),
    getSelectedMembers: () => Array.from(selectedMembers),
    searchMembers: (query) => setSearchQuery(query),
    filterByRole: (role) => setFilterRole(role),
    sortMembers: (sortOption) => setSortBy(sortOption),
    getAnalytics: () => memberAnalytics
  }), [
    selectedMembers,
    memberAnalytics,
    handleRefreshMembers,
    handleSelectAll
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced filtering and sorting logic
  const filteredAndSortedMembers = useMemo(() => {
    if (!team?.members) return [];

    let filtered = team.members.filter(member => {
      const matchesSearch = !searchQuery ||
        member.full_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        member.email?.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesRole = filterRole === 'all' || member.role === filterRole;

      return matchesSearch && matchesRole;
    });

    // Sort members
    filtered.sort((a, b) => {
      switch (sortBy) {
        case SORT_OPTIONS.NAME:
          return a.full_name.localeCompare(b.full_name);
        case SORT_OPTIONS.ROLE: {
          const roleOrder = { owner: 0, admin: 1, member: 2, viewer: 3 };
          return (roleOrder[a.role] || 4) - (roleOrder[b.role] || 4);
        }
        case SORT_OPTIONS.JOINED:
          return new Date(b.joined_at || 0) - new Date(a.joined_at || 0);
        case SORT_OPTIONS.ACTIVITY:
          return new Date(b.last_activity || 0) - new Date(a.last_activity || 0);
        default:
          return 0;
      }
    });

    return filtered;
  }, [team?.members, searchQuery, filterRole, sortBy]);

  // Enhanced event handlers
  const handleRefreshMembers = useCallback(async () => {
    setLoading(true);
    try {
      if (onTeamUpdated) {
        await onTeamUpdated();
      }
      setMemberAnalytics(prev => ({
        ...prev,
        totalMembers: team?.members?.length || 0,
        lastActivity: new Date().toISOString()
      }));
      announceToScreenReader('Team members refreshed successfully');
    } catch {
      announceToScreenReader('Failed to refresh team members');
    } finally {
      setLoading(false);
    }
  }, [onTeamUpdated, team?.members?.length, announceToScreenReader]);

  const handleSelectAll = useCallback(() => {
    if (selectedMembers.size === filteredAndSortedMembers.length) {
      setSelectedMembers(new Set());
      announceToScreenReader('All members deselected');
    } else {
      setSelectedMembers(new Set(filteredAndSortedMembers.map(member => member.user_id)));
      announceToScreenReader(`${filteredAndSortedMembers.length} members selected`);
    }
  }, [selectedMembers.size, filteredAndSortedMembers, announceToScreenReader]);

  const handleSelectMember = useCallback((memberId) => {
    setSelectedMembers(prev => {
      const newSet = new Set(prev);
      if (newSet.has(memberId)) {
        newSet.delete(memberId);
      } else {
        newSet.add(memberId);
      }
      return newSet;
    });
  }, []);

  // Get role display text and color
  const getRoleDisplay = useCallback((role) => {
    const roleConfig = {
      [TEAM_ROLES.OWNER]: {
        text: 'Owner',
        color: ACE_COLORS.YELLOW,
        backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1)
      },
      [TEAM_ROLES.ADMIN]: {
        text: 'Admin',
        color: ACE_COLORS.PURPLE,
        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
      },
      [TEAM_ROLES.MEMBER]: {
        text: 'Member',
        color: '#4CAF50',
        backgroundColor: alpha('#4CAF50', 0.1)
      },
      [TEAM_ROLES.VIEWER]: {
        text: 'Viewer',
        color: ACE_COLORS.DARK,
        backgroundColor: alpha(ACE_COLORS.DARK, 0.1)
      }
    };

    const config = roleConfig[role] || {
      text: role,
      color: ACE_COLORS.DARK,
      backgroundColor: alpha(ACE_COLORS.DARK, 0.1)
    };

    return config;
  }, []);

  // Enhanced role icon with styling
  const getRoleIcon = useCallback((role) => {
    const iconProps = { fontSize: 'small', sx: { color: 'inherit' } };

    switch (role) {
      case TEAM_ROLES.OWNER:
        return <StarIcon {...iconProps} />;
      case TEAM_ROLES.ADMIN:
        return <AdminPanelSettingsIcon {...iconProps} />;
      case TEAM_ROLES.MEMBER:
        return <PersonIcon {...iconProps} />;
      case TEAM_ROLES.VIEWER:
        return <VisibilityIcon {...iconProps} />;
      default:
        return <PersonIcon {...iconProps} />;
    }
  }, []);

  // Get member status
  const getMemberStatus = useCallback((member) => {
    // Mock status based on last activity - in real app this would come from API
    if (!member.last_activity) return MEMBER_STATUS.OFFLINE;

    const lastActivity = new Date(member.last_activity);
    const now = new Date();
    const minutesAgo = (now - lastActivity) / (1000 * 60);

    if (minutesAgo < 5) return MEMBER_STATUS.ONLINE;
    if (minutesAgo < 30) return MEMBER_STATUS.AWAY;
    return MEMBER_STATUS.OFFLINE;
  }, []);

  const getStatusColor = useCallback((status) => {
    switch (status) {
      case MEMBER_STATUS.ONLINE:
        return '#4CAF50';
      case MEMBER_STATUS.AWAY:
        return ACE_COLORS.YELLOW;
      case MEMBER_STATUS.BUSY:
        return '#F44336';
      case MEMBER_STATUS.OFFLINE:
      default:
        return '#9E9E9E';
    }
  }, []);

  // Enhanced menu handlers
  const handleMenuOpen = useCallback((event, member) => {
    setMenuAnchorEl(event.currentTarget);
    setSelectedMember(member);

    if (onAnalyticsTrack) {
      onAnalyticsTrack({
        action: 'member_menu_opened',
        memberId: member.user_id,
        timestamp: new Date().toISOString()
      });
    }

    announceToScreenReader(`Member options menu opened for ${member.full_name}`);
  }, [onAnalyticsTrack, announceToScreenReader]);

  const handleMenuClose = useCallback(() => {
    setMenuAnchorEl(null);
    setSelectedMember(null);
    announceToScreenReader('Member options menu closed');
  }, [announceToScreenReader]);

  // Enhanced member actions
  const handleRemoveMember = useCallback(() => {
    handleMenuClose();

    if (!selectedMember) return;

    showConfirmation({
      title: 'Remove Team Member',
      message: `Are you sure you want to remove ${selectedMember.full_name} from the team? This action cannot be undone.`,
      confirmText: 'Remove',
      confirmColor: 'error',
      onConfirm: async () => {
        setLoading(true);
        try {
          const success = await removeTeamMember(team.id, selectedMember.user_id);
          if (success) {
            if (onTeamUpdated) {
              onTeamUpdated();
            }

            if (onMemberAction) {
              onMemberAction({
                action: 'removed',
                member: selectedMember
              });
            }

            if (onAnalyticsTrack) {
              onAnalyticsTrack({
                action: 'member_removed',
                memberId: selectedMember.user_id,
                timestamp: new Date().toISOString()
              });
            }

            announceToScreenReader(`${selectedMember.full_name} removed from team`);
          }
        } catch {
          setError('Failed to remove team member');
        } finally {
          setLoading(false);
        }
      }
    });
  }, [
    handleMenuClose,
    selectedMember,
    showConfirmation,
    team.id,
    removeTeamMember,
    onTeamUpdated,
    onMemberAction,
    onAnalyticsTrack,
    announceToScreenReader
  ]);

  const handleEditMember = useCallback(() => {
    handleMenuClose();
    setEditingMember(selectedMember);
    setShowMemberDialog(true);
    announceToScreenReader(`Editing ${selectedMember?.full_name}`);
  }, [handleMenuClose, selectedMember, announceToScreenReader]);

  const handleUpdateMemberRole = useCallback(async (memberId, newRole) => {
    setLoading(true);
    try {
      const success = await updateTeamMemberRole(team.id, memberId, newRole);
      if (success) {
        if (onTeamUpdated) {
          onTeamUpdated();
        }

        if (onMemberAction) {
          onMemberAction({
            action: 'role_updated',
            memberId,
            newRole
          });
        }

        announceToScreenReader(`Member role updated to ${newRole}`);
        setShowMemberDialog(false);
        setEditingMember(null);
      }
    } catch {
      setError('Failed to update member role');
    } finally {
      setLoading(false);
    }
  }, [team.id, updateTeamMemberRole, onTeamUpdated, onMemberAction, announceToScreenReader]);

  // Check if current user is the owner
  const isOwner = team?.members?.find(member => member.user_id === user?.id)?.role === 'owner';

  // Paginated members
  const paginatedMembers = useMemo(() => {
    const startIndex = (currentPage - 1) * maxMembersPerPage;
    return filteredAndSortedMembers.slice(startIndex, startIndex + maxMembersPerPage);
  }, [filteredAndSortedMembers, currentPage, maxMembersPerPage]);

  return (
    <Box sx={{ ...glassMorphismStyles, p: 3 }}>
      {/* Enhanced Header */}
      <Box sx={{ mb: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box>
            <Typography variant="h4" fontWeight="bold" sx={{
              color: ACE_COLORS.DARK,
              background: `linear-gradient(135deg, ${ACE_COLORS.PURPLE} 0%, ${ACE_COLORS.YELLOW} 100%)`,
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>
              Team Members
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Manage your team members and their roles
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Refresh Members">
              <IconButton
                onClick={handleRefreshMembers}
                disabled={loading}
                sx={{ color: ACE_COLORS.PURPLE }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>

            {enableBulkOperations && selectedMembers.size > 0 && (
              <Chip
                label={`${selectedMembers.size} selected`}
                onDelete={() => setSelectedMembers(new Set())}
                sx={{
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                  color: ACE_COLORS.PURPLE
                }}
              />
            )}
          </Box>
        </Box>

        {/* Enhanced Filters and Search */}
        {enableAdvancedFilters && (
          <Stack direction="row" spacing={2} flexWrap="wrap" useFlexGap>
            <TextField
              size="small"
              placeholder="Search members..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon fontSize="small" sx={{ color: ACE_COLORS.PURPLE }} />
                  </InputAdornment>
                ),
              }}
              sx={{
                minWidth: 250,
                '& .MuiOutlinedInput-root': {
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: ACE_COLORS.PURPLE
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: ACE_COLORS.PURPLE
                  }
                }
              }}
            />

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Role</InputLabel>
              <Select
                value={filterRole}
                label="Role"
                onChange={(e) => setFilterRole(e.target.value)}
              >
                <MenuItem value="all">All Roles</MenuItem>
                <MenuItem value={TEAM_ROLES.OWNER}>Owner</MenuItem>
                <MenuItem value={TEAM_ROLES.ADMIN}>Admin</MenuItem>
                <MenuItem value={TEAM_ROLES.MEMBER}>Member</MenuItem>
                <MenuItem value={TEAM_ROLES.VIEWER}>Viewer</MenuItem>
              </Select>
            </FormControl>

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Sort By</InputLabel>
              <Select
                value={sortBy}
                label="Sort By"
                onChange={(e) => setSortBy(e.target.value)}
              >
                <MenuItem value={SORT_OPTIONS.NAME}>Name</MenuItem>
                <MenuItem value={SORT_OPTIONS.ROLE}>Role</MenuItem>
                <MenuItem value={SORT_OPTIONS.JOINED}>Joined Date</MenuItem>
                <MenuItem value={SORT_OPTIONS.ACTIVITY}>Last Activity</MenuItem>
              </Select>
            </FormControl>
          </Stack>
        )}

        {/* Results Summary */}
        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Showing {paginatedMembers.length} of {filteredAndSortedMembers.length} members
            {filteredAndSortedMembers.length !== (team?.members?.length || 0) &&
              ` (filtered from ${team?.members?.length || 0} total)`
            }
          </Typography>

          {enableBulkOperations && (
            <FormControlLabel
              control={
                <Checkbox
                  checked={selectedMembers.size === filteredAndSortedMembers.length && filteredAndSortedMembers.length > 0}
                  indeterminate={selectedMembers.size > 0 && selectedMembers.size < filteredAndSortedMembers.length}
                  onChange={handleSelectAll}
                  sx={{
                    color: ACE_COLORS.PURPLE,
                    '&.Mui-checked': {
                      color: ACE_COLORS.PURPLE
                    }
                  }}
                />
              }
              label="Select All"
            />
          )}
        </Box>

        {/* Loading Progress */}
        {loading && (
          <Box sx={{ mt: 1 }}>
            <LinearProgress
              sx={{
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                '& .MuiLinearProgress-bar': {
                  backgroundColor: ACE_COLORS.PURPLE
                }
              }}
            />
          </Box>
        )}
      </Box>

      {/* Error Alert */}
      {error && (
        <Fade in timeout={500}>
          <Alert
            severity="error"
            sx={{
              mb: 3,
              border: `1px solid ${alpha('#F44336', 0.3)}`,
              backgroundColor: alpha('#F44336', 0.1)
            }}
            action={
              <Button
                size="small"
                onClick={() => setError(null)}
                sx={{ color: '#F44336' }}
              >
                Dismiss
              </Button>
            }
          >
            {error}
          </Alert>
        </Fade>
      )}

      {/* Enhanced Members List */}
      <List sx={{ p: 0 }}>
        {paginatedMembers.map((member, index) => {
          const roleConfig = getRoleDisplay(member.role);
          const roleIcon = getRoleIcon(member.role);
          const isCurrentUser = member.user_id === user?.id;
          const memberStatus = getMemberStatus(member);
          const statusColor = getStatusColor(memberStatus);

          return (
            <Zoom in timeout={300 + index * 50} key={member.user_id}>
              <Card
                sx={{
                  mb: 2,
                  border: `2px solid ${alpha(ACE_COLORS.PURPLE, 0.1)}`,
                  borderRadius: 3,
                  background: `linear-gradient(135deg,
                    ${alpha(theme.palette.background.paper, 0.95)} 0%,
                    ${alpha(ACE_COLORS.PURPLE, 0.03)} 100%)`,
                  transition: 'all 300ms ease',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: `0 8px 25px ${alpha(ACE_COLORS.PURPLE, 0.15)}`
                  }
                }}
              >
                <ListItem sx={{ p: 3 }}>
                  {enableBulkOperations && (
                    <ListItemAvatar>
                      <Checkbox
                        checked={selectedMembers.has(member.user_id)}
                        onChange={() => handleSelectMember(member.user_id)}
                        sx={{
                          color: ACE_COLORS.PURPLE,
                          '&.Mui-checked': {
                            color: ACE_COLORS.PURPLE
                          }
                        }}
                      />
                    </ListItemAvatar>
                  )}

                  <ListItemAvatar>
                    <Badge
                      overlap="circular"
                      anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                      badgeContent={
                        showOnlineStatus && (
                          <CircleIcon
                            sx={{
                              color: statusColor,
                              fontSize: 12,
                              border: `2px solid ${theme.palette.background.paper}`,
                              borderRadius: '50%'
                            }}
                          />
                        )
                      }
                    >
                      <Avatar
                        src={member.avatar}
                        alt={member.full_name}
                        sx={{
                          width: 50,
                          height: 50,
                          bgcolor: !member.avatar ? ACE_COLORS.PURPLE : undefined,
                          color: ACE_COLORS.WHITE,
                          fontSize: '1.5rem',
                          fontWeight: 'bold'
                        }}
                      >
                        {!member.avatar && member.full_name?.charAt(0)?.toUpperCase()}
                      </Avatar>
                    </Badge>
                  </ListItemAvatar>

                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center" gap={1} mb={1}>
                        <Typography variant="h6" fontWeight="bold" sx={{ color: ACE_COLORS.DARK }}>
                          {member.full_name}
                          {isCurrentUser && (
                            <Chip
                              size="small"
                              label="You"
                              sx={{
                                ml: 1,
                                backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
                                color: ACE_COLORS.DARK,
                                fontSize: '0.7rem'
                              }}
                            />
                          )}
                        </Typography>

                        <Chip
                          size="small"
                          label={roleConfig.text}
                          icon={roleIcon}
                          sx={{
                            backgroundColor: roleConfig.backgroundColor,
                            color: roleConfig.color,
                            fontWeight: 'bold',
                            border: `1px solid ${alpha(roleConfig.color, 0.3)}`
                          }}
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Box display="flex" alignItems="center" gap={1} sx={{ mb: 1 }}>
                          <EmailIcon fontSize="small" sx={{ color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            {member.email}
                          </Typography>
                        </Box>

                        {member.last_activity && (
                          <Box display="flex" alignItems="center" gap={1}>
                            <TimeIcon fontSize="small" sx={{ color: 'text.secondary' }} />
                            <Typography variant="caption" color="text.secondary">
                              Last active {formatDistanceToNow(new Date(member.last_activity), { addSuffix: true })}
                            </Typography>
                          </Box>
                        )}
                      </Box>
                    }
                  />

                  {isOwnerOrAdmin && member.role !== TEAM_ROLES.OWNER && !isCurrentUser && (
                    <ListItemSecondaryAction>
                      <Tooltip title="Member Options">
                        <IconButton
                          edge="end"
                          onClick={(e) => handleMenuOpen(e, member)}
                          sx={{ color: ACE_COLORS.PURPLE }}
                        >
                          <MoreVertIcon />
                        </IconButton>
                      </Tooltip>
                    </ListItemSecondaryAction>
                  )}
                </ListItem>
              </Card>
            </Zoom>
          );
        })}
      </List>

      {/* Enhanced Member Options Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        slotProps={{
          paper: {
            sx: {
              ...glassMorphismStyles,
              border: `2px solid ${alpha(ACE_COLORS.PURPLE, 0.3)}`
            }
          }
        }}
      >
        <MenuItem onClick={handleEditMember}>
          <EditIcon fontSize="small" sx={{ mr: 1, color: ACE_COLORS.PURPLE }} />
          Edit Role
        </MenuItem>
        <MenuItem onClick={handleRemoveMember} sx={{ color: '#F44336' }}>
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
          Remove from Team
        </MenuItem>
      </Menu>

      {/* Member Edit Dialog */}
      <Dialog
        open={showMemberDialog}
        onClose={() => setShowMemberDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ color: ACE_COLORS.DARK }}>
          Edit Member Role
        </DialogTitle>
        <DialogContent>
          {editingMember && (
            <Box sx={{ pt: 2 }}>
              <Typography variant="body1" gutterBottom>
                Update role for {editingMember.full_name}
              </Typography>
              <FormControl fullWidth sx={{ mt: 2 }}>
                <InputLabel>Role</InputLabel>
                <Select
                  value={editingMember.role}
                  label="Role"
                  onChange={(e) => setEditingMember(prev => ({ ...prev, role: e.target.value }))}
                >
                  <MenuItem value={TEAM_ROLES.ADMIN}>Admin</MenuItem>
                  <MenuItem value={TEAM_ROLES.MEMBER}>Member</MenuItem>
                  <MenuItem value={TEAM_ROLES.VIEWER}>Viewer</MenuItem>
                </Select>
              </FormControl>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowMemberDialog(false)}>
            Cancel
          </Button>
          <Button
            onClick={() => handleUpdateMemberRole(editingMember?.user_id, editingMember?.role)}
            variant="contained"
            disabled={loading}
            sx={{
              backgroundColor: ACE_COLORS.PURPLE,
              '&:hover': {
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8)
              }
            }}
          >
            Update Role
          </Button>
        </DialogActions>
      </Dialog>

      {/* Notifications */}
      {notifications.map((notification, index) => (
        <Snackbar
          key={index}
          open={true}
          autoHideDuration={6000}
          onClose={() => setNotifications(prev => prev.filter((_, i) => i !== index))}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            severity={notification.severity || 'info'}
            onClose={() => setNotifications(prev => prev.filter((_, i) => i !== index))}
            sx={{
              ...glassMorphismStyles,
              border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.3)}`
            }}
          >
            {notification.message}
          </Alert>
        </Snackbar>
      ))}
    </Box>
  );
}));

TeamMembersList.displayName = 'TeamMembersList';

TeamMembersList.propTypes = {
  /** Team object with members data */
  team: PropTypes.shape({
    id: PropTypes.string.isRequired,
    members: PropTypes.arrayOf(PropTypes.shape({
      user_id: PropTypes.string.isRequired,
      full_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      role: PropTypes.string.isRequired,
      avatar: PropTypes.string,
      last_activity: PropTypes.string,
      joined_at: PropTypes.string
    }))
  }).isRequired,
  /** Whether current user is owner or admin */
  isOwnerOrAdmin: PropTypes.bool,
  /** Function called when team is updated */
  onTeamUpdated: PropTypes.func,
  /** Function called when member action is performed */
  onMemberAction: PropTypes.func,
  /** Function called for analytics tracking */
  onAnalyticsTrack: PropTypes.func,
  /** Enable bulk operations */
  enableBulkOperations: PropTypes.bool,
  /** Enable advanced filters */
  enableAdvancedFilters: PropTypes.bool,
  /** Enable real-time updates */
  enableRealTimeUpdates: PropTypes.bool,
  /** Enable member profiles */
  enableMemberProfiles: PropTypes.bool,
  /** Maximum members per page */
  maxMembersPerPage: PropTypes.number,
  /** Default view mode */
  defaultViewMode: PropTypes.oneOf(Object.values(VIEW_MODES)),
  /** Show online status */
  showOnlineStatus: PropTypes.bool
};

export default TeamMembersList;
