"""
Competitor service for handling competitor-related operations with price tracking capabilities.
"""
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta, timezone

from bson import ObjectId

from app.models.competitor import Competitor
from app.db.repositories.competitors import (
    get_competitor_by_id,
    get_competitors_by_user
)

# Set up logging
logger = logging.getLogger(__name__)

class CompetitorNotFoundError(Exception):
    """Exception raised when a competitor is not found."""
    pass

class CompetitorService:
    """Service for handling competitor-related operations with price tracking capabilities."""

    def __init__(self):
        self.redis_client = None  # Will be initialized when needed
        self.price_tracking_enabled = True

    async def get_competitor(self, competitor_id: str, user_id: str) -> Optional[Competitor]:
        """
        Get a competitor by ID and verify it belongs to the user.

        Args:
            competitor_id: The ID of the competitor
            user_id: The ID of the user

        Returns:
            Competitor object if found and belongs to user, None otherwise
        """
        competitor = await get_competitor_by_id(competitor_id)
        if not competitor:
            return None

        # Check if competitor belongs to user
        if str(competitor.user_id) != user_id:
            return None

        return competitor

    async def track_competitor_prices(self, competitor_id: str, user_id: str, products: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Start tracking prices for competitor products.

        Args:
            competitor_id: The ID of the competitor
            user_id: The ID of the user
            products: List of products to track with URLs and identifiers

        Returns:
            Dictionary with tracking status and configuration
        """
        try:
            from app.db.mongodb import get_database
            from app.core.redis import get_redis_client

            # Verify competitor ownership
            competitor = await self.get_competitor(competitor_id, user_id)
            if not competitor:
                raise ValueError("Competitor not found or access denied")

            db = await get_database()
            redis_client = await get_redis_client()

            # Create price tracking configuration
            tracking_config = {
                "competitor_id": competitor_id,
                "user_id": user_id,
                "products": products,
                "tracking_enabled": True,
                "check_frequency": "hourly",  # hourly, daily, weekly
                "price_change_threshold": 0.05,  # 5% change threshold
                "alert_settings": {
                    "email_alerts": True,
                    "push_notifications": True,
                    "price_drop_alerts": True,
                    "price_increase_alerts": False
                },
                "created_at": datetime.now(timezone.utc),
                "last_checked": None
            }

            # Store in MongoDB
            await db.competitor_price_tracking.insert_one(tracking_config)

            # Cache in Redis for quick access
            if redis_client:
                cache_key = f"price_tracking:{competitor_id}:{user_id}"
                await redis_client.setex(
                    cache_key,
                    3600,  # 1 hour TTL
                    json.dumps(tracking_config, default=str)
                )

            return {
                "status": "success",
                "message": f"Price tracking enabled for {len(products)} products",
                "tracking_id": str(tracking_config.get("_id")),
                "products_tracked": len(products),
                "check_frequency": tracking_config["check_frequency"]
            }

        except Exception as e:
            logger.error(f"Failed to enable price tracking: {str(e)}")
            raise

    async def get_price_alerts(self, user_id: str, competitor_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get price alerts for user's tracked competitors.

        Args:
            user_id: The ID of the user
            competitor_id: Optional specific competitor ID

        Returns:
            List of price alerts
        """
        try:
            from app.db.mongodb import get_database

            db = await get_database()

            # Build query
            query: Dict[str, Any] = {"user_id": user_id}
            if competitor_id:
                query["competitor_id"] = competitor_id

            # Get recent price alerts (last 30 days)
            thirty_days_ago = datetime.now(timezone.utc) - timedelta(days=30)
            query["created_at"] = {"$gte": thirty_days_ago}

            alerts = await db.competitor_price_alerts.find(query).sort("created_at", -1).to_list(100)

            return [
                {
                    "id": str(alert["_id"]),
                    "competitor_id": alert["competitor_id"],
                    "product_name": alert.get("product_name"),
                    "product_url": alert.get("product_url"),
                    "old_price": alert.get("old_price"),
                    "new_price": alert.get("new_price"),
                    "price_change": alert.get("price_change"),
                    "price_change_percentage": alert.get("price_change_percentage"),
                    "alert_type": alert.get("alert_type", "price_change"),
                    "created_at": alert["created_at"],
                    "is_read": alert.get("is_read", False)
                }
                for alert in alerts
            ]

        except Exception as e:
            logger.error(f"Failed to get price alerts: {str(e)}")
            raise
    
    async def get_competitor_posts(self, competitor_id: str, user_id: str) -> List[Dict[str, Any]]:
        """
        Get all posts for a specific competitor (deprecated, use get_competitor_posts_paginated instead)
        
        Args:
            competitor_id: The ID of the competitor
            user_id: The ID of the user
            
        Returns:
            List of post dictionaries
            
        Raises:
            CompetitorNotFoundError: If competitor not found or doesn't belong to user
        """
        # Check if competitor exists and belongs to user
        competitor = await self.get_competitor(competitor_id, user_id)
        if not competitor:
            raise CompetitorNotFoundError(f"Competitor with ID {competitor_id} not found")
            
        return [post.model_dump() for post in competitor.posts]
        
    async def get_competitor_posts_paginated(
        self, 
        competitor_id: str, 
        user_id: str,
        page: int = 1,
        limit: int = 10,
        filters: Optional[Dict[str, Any]] = None,
        sort_by: str = "post_date",
        sort_order: str = "desc"
    ) -> Dict[str, Any]:
        """
        Get paginated posts for a specific competitor with filtering and sorting
        
        Args:
            competitor_id: The ID of the competitor
            user_id: The ID of the user
            page: Page number (1-based)
            limit: Number of items per page
            filters: Dictionary of filters to apply
            sort_by: Field to sort by
            sort_order: Sort order ("asc" or "desc")
            
        Returns:
            Dictionary with posts and pagination info
            
        Raises:
            CompetitorNotFoundError: If competitor not found or doesn't belong to user
            ValueError: If invalid parameters provided
        """
        # Check if competitor exists and belongs to user
        competitor = await self.get_competitor(competitor_id, user_id)
        if not competitor:
            raise CompetitorNotFoundError(f"Competitor with ID {competitor_id} not found")
        
        # Convert posts to dictionaries for filtering and sorting
        posts_dicts = [post.model_dump() for post in competitor.posts]

        # Apply filters
        filtered_posts = posts_dicts
        if filters:
            for key, value in filters.items():
                filtered_posts = [post for post in filtered_posts if post.get(key) == value]

        # Sort posts
        reverse = sort_order.lower() == "desc"
        sorted_posts = sorted(
            filtered_posts,
            key=lambda post: post.get(sort_by, 0) if sort_by != "post_date" else post.get("post_date", ""),
            reverse=reverse
        )
        
        # Calculate pagination
        total_posts = len(sorted_posts)
        total_pages = (total_posts + limit - 1) // limit if total_posts > 0 else 1
        
        # Validate page number
        if page > total_pages:
            page = total_pages
        
        # Get paginated posts
        start_idx = (page - 1) * limit
        end_idx = min(start_idx + limit, total_posts)
        paginated_posts = sorted_posts[start_idx:end_idx]
        
        # Return paginated result
        return {
            "posts": paginated_posts,
            "pagination": {
                "page": page,
                "limit": limit,
                "total_posts": total_posts,
                "total_pages": total_pages,
                "has_previous": page > 1,
                "has_next": page < total_pages
            },
            "filters": filters or {},
            "sort": {
                "field": sort_by,
                "order": sort_order
            }
        }

def get_competitor_service() -> CompetitorService:
    """
    Factory function to create a CompetitorService instance.
    
    Returns:
        CompetitorService instance
    """
    return CompetitorService()
