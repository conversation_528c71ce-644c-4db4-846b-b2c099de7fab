/**
 * Enhanced Trial Page Container - Enterprise-grade trial-specific page container component
 * Features: Comprehensive trial page container with advanced trial header management, trial-specific
 * navigation, and trial state integration, detailed trial page customization with dynamic trial
 * headers and personalized trial navigation flows, advanced trial page features with trial progress
 * indicators and trial countdown displays, ACE Social's trial system integration with seamless trial
 * page lifecycle management, trial page interaction features including trial-specific breadcrumbs
 * and trial navigation shortcuts, trial page state management with real-time trial updates and
 * trial validation checks, real-time trial page updates with live trial displays and dynamic trial
 * messaging, and seamless ACE Social trial platform integration with advanced trial page orchestration
 * and comprehensive trial accessibility compliance
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 */

import {
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef
} from 'react';
import PropTypes from 'prop-types';
import { Helmet } from 'react-helmet-async';
import {
  Box,
  Container,
  Typography,
  Breadcrumbs,
  Link,
  Chip,
  Alert,
  LinearProgress,
  Stack,
  IconButton,
  Tooltip,
  Collapse,
  Paper,
  alpha,
  useTheme
} from '@mui/material';
import {
  Home as HomeIcon,
  NavigateNext as NavigateNextIcon,
  Schedule as ScheduleIcon,

  Info as InfoIcon,
  Upgrade as UpgradeIcon,
  Settings as SettingsIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,

} from '@mui/icons-material';
import { useTrial } from '../contexts/TrialContext';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Trial page types
const TRIAL_PAGE_TYPES = {
  TRIAL_DASHBOARD: 'trial_dashboard',
  TRIAL_FEATURES: 'trial_features',
  TRIAL_SETTINGS: 'trial_settings',
  TRIAL_BILLING: 'trial_billing',
  TRIAL_SUPPORT: 'trial_support'
};

// Trial navigation items
const TRIAL_NAVIGATION = {
  HOME: { label: 'Trial Dashboard', path: '/trial', icon: <HomeIcon /> },
  FEATURES: { label: 'Trial Features', path: '/trial/features', icon: <InfoIcon /> },
  SETTINGS: { label: 'Trial Settings', path: '/trial/settings', icon: <SettingsIcon /> },
  BILLING: { label: 'Upgrade Trial', path: '/billing/plans', icon: <UpgradeIcon /> }
};
/**
 * Enhanced Trial Page Container - Comprehensive trial page container with advanced features
 * Implements detailed trial page management and enterprise-grade trial page capabilities
 */
const TrialPageContainer = memo(forwardRef(({
  title,
  children,
  sx = {},
  pageType = TRIAL_PAGE_TYPES.TRIAL_DASHBOARD,
  showTrialHeader = true,
  showTrialProgress = true,
  showTrialNavigation = true,
  showTrialAlerts = true,
  enableAccessibility = true,
  enableAnalytics = true,
  customBreadcrumbs = null,
  onTrialAction,
  onAnalyticsTrack,
  onPageInteraction
}, ref) => {
  const theme = useTheme();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Core refs for component management
  const containerRef = useRef(null);
  const headerRef = useRef(null);

  // Trial context integration
  const {
    isTrial,
    daysRemaining,
    hoursRemaining,
    trialEnd,
    planId
  } = useTrial();

  // Enhanced state management
  const [showTrialDetails, setShowTrialDetails] = useState(false);
  const [pageAnalytics, setPageAnalytics] = useState({
    pageLoadTime: null,
    interactions: 0,
    scrollDepth: 0,
    timeOnPage: 0,
    lastActivity: new Date().toISOString()
  });
  const [trialAlerts, setTrialAlerts] = useState([]);

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    scrollToTop: () => containerRef.current?.scrollIntoView({ behavior: 'smooth' }),
    focusHeader: () => headerRef.current?.focus(),
    getPageAnalytics: () => pageAnalytics,
    resetAnalytics: () => setPageAnalytics({
      pageLoadTime: null,
      interactions: 0,
      scrollDepth: 0,
      timeOnPage: 0,
      lastActivity: new Date().toISOString()
    }),
    showTrialDetails: () => setShowTrialDetails(true),
    hideTrialDetails: () => setShowTrialDetails(false)
  }), [pageAnalytics]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced trial status calculation
  const trialStatus = useMemo(() => {
    if (!isTrial) return { type: 'inactive', message: 'Trial not active' };

    if (daysRemaining <= 0 && hoursRemaining <= 0) {
      return { type: 'expired', message: 'Trial expired' };
    } else if (daysRemaining === 0 && hoursRemaining <= 6) {
      return { type: 'critical', message: `${hoursRemaining} hours remaining` };
    } else if (daysRemaining <= 1) {
      return { type: 'warning', message: `${daysRemaining} day, ${hoursRemaining} hours remaining` };
    } else if (daysRemaining <= 3) {
      return { type: 'info', message: `${daysRemaining} days remaining` };
    }

    return { type: 'active', message: `${daysRemaining} days remaining` };
  }, [isTrial, daysRemaining, hoursRemaining]);

  // Enhanced effects for component lifecycle
  useEffect(() => {
    setPageAnalytics(prev => ({
      ...prev,
      pageLoadTime: Date.now(),
      lastActivity: new Date().toISOString()
    }));

    if (enableAccessibility) {
      announceToScreenReader(`Trial page loaded: ${title || 'Trial Dashboard'}`);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack('trial_page_viewed', {
        pageType,
        title,
        trialStatus: trialStatus.type,
        timestamp: new Date().toISOString()
      });
    }
  }, [
    title,
    pageType,
    trialStatus.type,
    enableAccessibility,
    announceToScreenReader,
    enableAnalytics,
    onAnalyticsTrack
  ]);

  // Enhanced trial alerts management
  useEffect(() => {
    const alerts = [];

    if (isTrial && showTrialAlerts) {
      if (trialStatus.type === 'expired') {
        alerts.push({
          id: 'trial-expired',
          severity: 'error',
          message: 'Your trial has expired. Upgrade to continue using premium features.',
          action: { label: 'Upgrade Now', handler: () => window.location.href = '/billing/plans' }
        });
      } else if (trialStatus.type === 'critical') {
        alerts.push({
          id: 'trial-critical',
          severity: 'warning',
          message: `Trial expires in ${hoursRemaining} hours. Upgrade to avoid losing access.`,
          action: { label: 'Upgrade Now', handler: () => window.location.href = '/billing/plans' }
        });
      } else if (trialStatus.type === 'warning') {
        alerts.push({
          id: 'trial-warning',
          severity: 'info',
          message: `Trial expires in ${daysRemaining} day${daysRemaining !== 1 ? 's' : ''}. Consider upgrading soon.`,
          action: { label: 'View Plans', handler: () => window.location.href = '/billing/plans' }
        });
      }
    }

    setTrialAlerts(alerts);
  }, [isTrial, showTrialAlerts, trialStatus.type, daysRemaining, hoursRemaining]);

  // Enhanced handlers
  const handleTrialAction = useCallback((action, data) => {
    setPageAnalytics(prev => ({
      ...prev,
      interactions: prev.interactions + 1,
      lastActivity: new Date().toISOString()
    }));

    if (onTrialAction) {
      onTrialAction(action, {
        ...data,
        pageType,
        timestamp: new Date().toISOString()
      });
    }

    if (onPageInteraction) {
      onPageInteraction('trial_action', {
        action,
        data,
        timestamp: new Date().toISOString()
      });
    }
  }, [onTrialAction, onPageInteraction, pageType]);

  // Enhanced breadcrumb generation
  const generateBreadcrumbs = useCallback(() => {
    if (customBreadcrumbs) return customBreadcrumbs;

    const breadcrumbs = [
      { label: 'Trial Dashboard', path: '/trial', icon: <HomeIcon fontSize="small" /> }
    ];

    if (pageType !== TRIAL_PAGE_TYPES.TRIAL_DASHBOARD) {
      const currentPage = Object.values(TRIAL_NAVIGATION).find(nav =>
        nav.path === window.location.pathname
      );

      if (currentPage) {
        breadcrumbs.push({
          label: currentPage.label,
          path: currentPage.path,
          icon: currentPage.icon
        });
      }
    }

    return breadcrumbs;
  }, [customBreadcrumbs, pageType]);

  return (
    <>
      {/* Enhanced Helmet with trial-specific title */}
      {title && (
        <Helmet>
          <title>{isTrial ? `[TRIAL] ${title}` : title} - ACE Social</title>
          <meta name="description" content={`Trial page: ${title}. ${trialStatus.message}`} />
        </Helmet>
      )}

      <Container
        ref={containerRef}
        maxWidth="xl"
        sx={{
          py: 3,
          ...sx,
        }}
      >
        {/* Trial Alerts */}
        {showTrialAlerts && trialAlerts.length > 0 && (
          <Stack spacing={2} sx={{ mb: 3 }}>
            {trialAlerts.map((alert) => (
              <Alert
                key={alert.id}
                severity={alert.severity}
                action={
                  alert.action && (
                    <IconButton
                      color="inherit"
                      size="small"
                      onClick={alert.action.handler}
                    >
                      <UpgradeIcon />
                    </IconButton>
                  )
                }
                sx={{
                  ...glassMorphismStyles,
                  borderLeft: `4px solid ${
                    alert.severity === 'error' ? '#F44336' :
                    alert.severity === 'warning' ? ACE_COLORS.YELLOW :
                    ACE_COLORS.PURPLE
                  }`
                }}
              >
                <Typography variant="body2">
                  {alert.message}
                </Typography>
              </Alert>
            ))}
          </Stack>
        )}

        {/* Trial Header */}
        {showTrialHeader && isTrial && (
          <Paper
            ref={headerRef}
            elevation={2}
            sx={{
              ...glassMorphismStyles,
              p: 3,
              mb: 3,
              position: 'relative'
            }}
          >
            {/* Trial Progress Bar */}
            {showTrialProgress && (
              <LinearProgress
                variant="determinate"
                value={Math.max(0, Math.min(100, ((14 - daysRemaining) / 14) * 100))}
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: 4,
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.2),
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: trialStatus.type === 'critical' ? '#F44336' :
                                   trialStatus.type === 'warning' ? ACE_COLORS.YELLOW :
                                   ACE_COLORS.PURPLE
                  }
                }}
              />
            )}

            <Stack direction="row" alignItems="center" justifyContent="space-between" spacing={2}>
              <Box>
                <Stack direction="row" alignItems="center" spacing={2}>
                  <Typography variant="h5" component="h1" sx={{ fontWeight: 'bold' }}>
                    {title || 'Trial Dashboard'}
                  </Typography>
                  <Chip
                    label={`TRIAL - ${trialStatus.message.toUpperCase()}`}
                    color={
                      trialStatus.type === 'critical' ? 'error' :
                      trialStatus.type === 'warning' ? 'warning' :
                      'primary'
                    }
                    variant="outlined"
                    icon={<ScheduleIcon />}
                  />
                </Stack>

                {trialEnd && (
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    Trial expires: {new Date(trialEnd).toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </Typography>
                )}
              </Box>

              <Stack direction="row" spacing={1}>
                <Tooltip title="Trial details">
                  <IconButton
                    onClick={() => setShowTrialDetails(!showTrialDetails)}
                    size="small"
                  >
                    {showTrialDetails ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                  </IconButton>
                </Tooltip>
                <Tooltip title="Upgrade trial">
                  <IconButton
                    onClick={() => handleTrialAction('upgrade_clicked', { source: 'header' })}
                    size="small"
                    sx={{
                      backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                      '&:hover': {
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.2)
                      }
                    }}
                  >
                    <UpgradeIcon />
                  </IconButton>
                </Tooltip>
              </Stack>
            </Stack>

            {/* Trial Details Collapse */}
            <Collapse in={showTrialDetails}>
              <Box sx={{ mt: 2, pt: 2, borderTop: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.1)}` }}>
                <Stack direction="row" spacing={4}>
                  <Box>
                    <Typography variant="caption" color="text.secondary">
                      Plan ID
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                      {planId || 'Trial'}
                    </Typography>
                  </Box>
                  <Box>
                    <Typography variant="caption" color="text.secondary">
                      Days Remaining
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                      {daysRemaining}
                    </Typography>
                  </Box>
                  <Box>
                    <Typography variant="caption" color="text.secondary">
                      Hours Remaining
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                      {hoursRemaining}
                    </Typography>
                  </Box>
                </Stack>
              </Box>
            </Collapse>
          </Paper>
        )}

        {/* Trial Navigation Breadcrumbs */}
        {showTrialNavigation && (
          <Box sx={{ mb: 3 }}>
            <Breadcrumbs
              separator={<NavigateNextIcon fontSize="small" />}
              aria-label="trial navigation breadcrumb"
            >
              {generateBreadcrumbs().map((crumb, index) => (
                <Link
                  key={index}
                  color="inherit"
                  href={crumb.path}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 0.5,
                    textDecoration: 'none',
                    '&:hover': {
                      textDecoration: 'underline'
                    }
                  }}
                >
                  {crumb.icon}
                  {crumb.label}
                </Link>
              ))}
            </Breadcrumbs>
          </Box>
        )}

        {/* Main Content */}
        <Box
          sx={{
            position: 'relative',
            minHeight: '400px'
          }}
        >
          {children}
        </Box>
      </Container>
    </>
  );
}));

TrialPageContainer.displayName = 'TrialPageContainer';

TrialPageContainer.propTypes = {
  /** Page title to be displayed in the browser tab */
  title: PropTypes.string,
  /** Child components to be rendered inside the container */
  children: PropTypes.node.isRequired,
  /** Additional styles to apply to the container */
  sx: PropTypes.object,
  /** Trial page type for analytics and behavior */
  pageType: PropTypes.oneOf(Object.values(TRIAL_PAGE_TYPES)),
  /** Show trial header section */
  showTrialHeader: PropTypes.bool,
  /** Show trial progress indicator */
  showTrialProgress: PropTypes.bool,
  /** Show trial navigation breadcrumbs */
  showTrialNavigation: PropTypes.bool,
  /** Show trial alerts */
  showTrialAlerts: PropTypes.bool,
  /** Enable accessibility features */
  enableAccessibility: PropTypes.bool,
  /** Enable analytics tracking */
  enableAnalytics: PropTypes.bool,
  /** Custom breadcrumbs override */
  customBreadcrumbs: PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.string.isRequired,
    path: PropTypes.string.isRequired,
    icon: PropTypes.node
  })),
  /** Trial action callback */
  onTrialAction: PropTypes.func,
  /** Analytics tracking callback */
  onAnalyticsTrack: PropTypes.func,
  /** Page interaction callback */
  onPageInteraction: PropTypes.func
};

export default TrialPageContainer;
