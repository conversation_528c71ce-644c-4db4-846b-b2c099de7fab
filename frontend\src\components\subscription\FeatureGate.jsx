/**
 * Enhanced Feature Gate - Enterprise-grade feature access control component
 * Features: Comprehensive feature gating system with advanced subscription plan validation capabilities,
 * real-time subscription status synchronization with billing system integration, detailed feature access
 * analytics with usage tracking and plan comparison insights, ACE Social's 3-tier subscription structure
 * integration (creator/accelerator/dominator plans), advanced feature gate interaction features including
 * upgrade prompts and trial extensions, feature gate customization capabilities with conditional rendering
 * and progressive disclosure, feature access scheduling and temporary unlock controls with time-based
 * restrictions, and seamless ACE Social platform integration with advanced feature access control
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 */

import {
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Chip,
  Alert,
  AlertTitle,
  LinearProgress,
  Stack,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
  Badge,
  Skeleton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
  alpha,
  useMediaQuery,
  Fade,
  Slide,
  Zoom,
  Collapse,
  CircularProgress,
  Snackbar
} from '@mui/material';
import {
  Lock as LockIcon,
  Upgrade as UpgradeIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  Close as CloseIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Timer as TimerIcon,
  Security as SecurityIcon,
  Speed as SpeedIcon,
  Analytics as AnalyticsIcon,
  Star as StarIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Schedule as ScheduleIcon,
  MonetizationOn as MonetizationOnIcon,
  Assessment as AssessmentIcon,
  Notifications as NotificationsIcon,
  Launch as LaunchIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useSubscription } from '../../contexts/SubscriptionContext';
import { useNotification } from '../../hooks/useNotification';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Feature gate variants
const GATE_VARIANTS = {
  CARD: 'card',
  INLINE: 'inline',
  ALERT: 'alert',
  MODAL: 'modal',
  BANNER: 'banner',
  TOOLTIP: 'tooltip'
};

// Access denial reasons
const DENIAL_REASONS = {
  PLAN_LIMIT: 'plan_limit',
  USAGE_LIMIT: 'usage_limit',
  ROLE_PERMISSION: 'role_permission',
  TRIAL_EXPIRED: 'trial_expired',
  FEATURE_DISABLED: 'feature_disabled',
  MAINTENANCE: 'maintenance'
};

// ACE Social 3-tier subscription plans with feature access
const SUBSCRIPTION_PLANS = {
  creator: {
    id: 'creator',
    name: 'Creator',
    tier: 1,
    price: 19,
    yearlyPrice: 190,
    color: ACE_COLORS.YELLOW,
    features: {
      ai_auto_replies: { limit: 100, enabled: true },
      regeneration: { limit: 25, enabled: true },
      policy_compliance: { limit: -1, enabled: false },
      document_training: { limit: 0, enabled: false },
      brand_voice_consistency: { limit: -1, enabled: false },
      white_label_ai_responses: { limit: 0, enabled: false },
      multi_language_ai_support: { limit: 0, enabled: false },
      advanced_scheduling: { limit: -1, enabled: false },
      team_collaboration: { limit: 0, enabled: false },
      ab_testing: { limit: 0, enabled: false },
      analytics_export: { limit: 0, enabled: false },
      custom_integrations: { limit: 0, enabled: false }
    }
  },
  accelerator: {
    id: 'accelerator',
    name: 'Accelerator',
    tier: 2,
    price: 49,
    yearlyPrice: 490,
    color: ACE_COLORS.PURPLE,
    features: {
      ai_auto_replies: { limit: 500, enabled: true },
      regeneration: { limit: 100, enabled: true },
      policy_compliance: { limit: -1, enabled: true },
      document_training: { limit: 10, enabled: true },
      brand_voice_consistency: { limit: -1, enabled: true },
      white_label_ai_responses: { limit: 0, enabled: false },
      multi_language_ai_support: { limit: 5, enabled: true },
      advanced_scheduling: { limit: -1, enabled: true },
      team_collaboration: { limit: 5, enabled: true },
      ab_testing: { limit: 10, enabled: true },
      analytics_export: { limit: 10, enabled: true },
      custom_integrations: { limit: 3, enabled: true }
    }
  },
  dominator: {
    id: 'dominator',
    name: 'Dominator',
    tier: 3,
    price: 99,
    yearlyPrice: 990,
    color: ACE_COLORS.DARK,
    features: {
      ai_auto_replies: { limit: -1, enabled: true },
      regeneration: { limit: -1, enabled: true },
      policy_compliance: { limit: -1, enabled: true },
      document_training: { limit: -1, enabled: true },
      brand_voice_consistency: { limit: -1, enabled: true },
      white_label_ai_responses: { limit: -1, enabled: true },
      multi_language_ai_support: { limit: -1, enabled: true },
      advanced_scheduling: { limit: -1, enabled: true },
      team_collaboration: { limit: -1, enabled: true },
      ab_testing: { limit: -1, enabled: true },
      analytics_export: { limit: -1, enabled: true },
      custom_integrations: { limit: -1, enabled: true }
    }
  }
};

/**
 * Enhanced Feature Gate - Comprehensive feature access control with advanced subscription validation
 * Implements plan-based feature restrictions and enterprise-grade access control capabilities
 */
const FeatureGate = memo(forwardRef(({
  feature,
  action,
  quantity = 1,
  children,
  fallback,
  showUpgrade = true,
  showUsage = true,
  customMessage,
  variant = GATE_VARIANTS.CARD,
  requiresRole = null,
  showTrialPrompt = true,
  enableRealTimeUpdates = true,
  enableAnalytics = true,
  enablePreview = false,
  temporaryAccess = false,
  temporaryDuration = 0,
  onUpgradeClick,
  onAccessDenied,
  onFeaturePreview,
  onAnalyticsTrack,
  onTemporaryAccessExpired
}, ref) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { showSuccessNotification, showWarningNotification } = useNotification();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Responsive design
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Core state management
  const temporaryAccessTimeoutRef = useRef(null);

  // Enhanced state management
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [upgradeInProgress, setUpgradeInProgress] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [previewActive, setPreviewActive] = useState(false);
  const [temporaryAccessActive, setTemporaryAccessActive] = useState(temporaryAccess);
  const [temporaryTimeRemaining, setTemporaryTimeRemaining] = useState(temporaryDuration);
  const [analyticsData, setAnalyticsData] = useState(null);
  const [accessAttempts, setAccessAttempts] = useState(0);
  const [lastAccessAttempt, setLastAccessAttempt] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  // Subscription context
  const {
    subscription,
    hasFeatureAccess,
    canPerformAction,
    hasRolePermission,
    getRemainingUsage,
    getUsagePercentage,
    needsUpgradeFor,
    getRecommendedUpgrade,
    upgradeSubscription,
    isTrialActive,
    getTrialDaysRemaining,
    usageAlerts,
    loading: subscriptionLoading,
    getPlanTier
  } = useSubscription();

  // Current subscription data
  const currentPlan = subscription ? SUBSCRIPTION_PLANS[subscription.plan_name?.toLowerCase()] : null;
  const planTier = getPlanTier();

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    checkAccess: () => hasFullAccess,
    getAccessStatus: () => ({
      hasFeatureAccess: hasFeatureAccess_,
      canPerformAction: canPerformAction_,
      hasRoleAccess,
      needsUpgrade,
      trialActive
    }),
    showUpgradeModal: () => setShowUpgradeModal(true),
    hideUpgradeModal: () => setShowUpgradeModal(false),
    startPreview: () => handleStartPreview(),
    stopPreview: () => handleStopPreview(),
    grantTemporaryAccess: (duration) => handleGrantTemporaryAccess(duration),
    revokeTemporaryAccess: () => handleRevokeTemporaryAccess(),
    getAnalytics: () => analyticsData,
    trackAccessAttempt: () => handleTrackAccessAttempt(),
    getUsageInfo: () => getUsageInfo(),
    getFeatureDescription: () => getFeatureDescription()
  }), [
    hasFeatureAccess_,
    canPerformAction_,
    hasRoleAccess,
    needsUpgrade,
    trialActive,
    analyticsData
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced event handlers
  const handleTrackAccessAttempt = useCallback(() => {
    setAccessAttempts(prev => prev + 1);
    setLastAccessAttempt(new Date().toISOString());

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack({
        event: 'feature_access_attempt',
        feature,
        action,
        timestamp: new Date().toISOString(),
        hasAccess: hasFullAccess,
        denialReason: !hasFullAccess ? getDenialReason() : null
      });
    }

    announceToScreenReader(`Access attempt recorded for ${feature || action}`);
  }, [feature, action, hasFullAccess, enableAnalytics, onAnalyticsTrack, announceToScreenReader]);

  const handleStartPreview = useCallback(() => {
    if (!enablePreview) return;

    setPreviewActive(true);
    setShowPreview(true);
    announceToScreenReader(`Feature preview started for ${feature || action}`);

    if (onFeaturePreview) {
      onFeaturePreview({ feature, action, previewStarted: true });
    }
  }, [enablePreview, feature, action, onFeaturePreview, announceToScreenReader]);

  const handleStopPreview = useCallback(() => {
    setPreviewActive(false);
    setShowPreview(false);
    announceToScreenReader(`Feature preview stopped for ${feature || action}`);

    if (onFeaturePreview) {
      onFeaturePreview({ feature, action, previewStarted: false });
    }
  }, [feature, action, onFeaturePreview, announceToScreenReader]);

  // Real-time usage alerts monitoring
  useEffect(() => {
    if (enableRealTimeUpdates && usageAlerts?.length > 0) {
      const relevantAlert = usageAlerts.find(alert =>
        alert.feature === feature || alert.feature === action
      );

      if (relevantAlert && relevantAlert.severity === 'high') {
        showWarningNotification(relevantAlert.message);
      }
    }
  }, [usageAlerts, feature, action, enableRealTimeUpdates, showWarningNotification]);

  // Enhanced loading state
  if (loading || subscriptionLoading) {
    return (
      <Box sx={{
        p: theme.spacing(2),
        textAlign: 'center',
        ...glassMorphismStyles
      }}>
        <Fade in timeout={300}>
          <Box>
            <CircularProgress
              size={40}
              sx={{
                color: ACE_COLORS.PURPLE,
                mb: theme.spacing(2)
              }}
            />
            <Typography variant="body2" sx={{ color: ACE_COLORS.PURPLE, fontWeight: 500 }}>
              Verifying feature access...
            </Typography>
          </Box>
        </Fade>
      </Box>
    );
  }

  // Enhanced access control checks
  const hasFeatureAccess_ = feature ? hasFeatureAccess(feature) : true;
  const canPerformAction_ = action ? canPerformAction(action, quantity) : true;
  const hasRoleAccess = requiresRole ? hasRolePermission(requiresRole) : true;
  const needsUpgrade = feature ? needsUpgradeFor(feature) : false;
  const recommendedPlan = feature ? getRecommendedUpgrade(feature) : null;
  const trialActive = isTrialActive();
  const trialDaysRemaining = getTrialDaysRemaining();

  // Check all access conditions including temporary access
  const hasFullAccess = (hasFeatureAccess_ && canPerformAction_ && hasRoleAccess) ||
                       temporaryAccessActive ||
                       previewActive;

  // Enhanced helper functions
  const getDenialReason = useCallback(() => {
    if (!hasRoleAccess) return DENIAL_REASONS.ROLE_PERMISSION;
    if (needsUpgrade) return DENIAL_REASONS.PLAN_LIMIT;
    if (!canPerformAction_) return DENIAL_REASONS.USAGE_LIMIT;
    if (trialActive && trialDaysRemaining <= 0) return DENIAL_REASONS.TRIAL_EXPIRED;
    return DENIAL_REASONS.FEATURE_DISABLED;
  }, [hasRoleAccess, needsUpgrade, canPerformAction_, trialActive, trialDaysRemaining]);

  // Get usage information with enhanced details
  const getUsageInfo = useCallback(() => {
    if (!action) return null;

    const remaining = getRemainingUsage(action.replace('_', '_'));
    const percentage = getUsagePercentage(action.replace('_', '_'));
    const planFeature = currentPlan?.features?.[feature];

    return {
      remaining,
      percentage,
      limit: planFeature?.limit || 0,
      enabled: planFeature?.enabled || false
    };
  }, [action, getRemainingUsage, getUsagePercentage, currentPlan, feature]);

  // Enhanced feature description with benefits
  const getFeatureDescription = useCallback(() => {
    const descriptions = {
      ai_auto_replies: {
        name: 'AI-powered automatic replies to comments and messages',
        benefits: ['Save time on customer engagement', 'Maintain consistent brand voice', '24/7 automated responses'],
        icon: <AnalyticsIcon />
      },
      regeneration: {
        name: 'AI content regeneration and optimization',
        benefits: ['Improve content quality', 'Generate multiple variations', 'Optimize for engagement'],
        icon: <TrendingUpIcon />
      },
      policy_compliance: {
        name: 'Policy compliance checking for automated responses',
        benefits: ['Ensure brand safety', 'Avoid policy violations', 'Automated compliance monitoring'],
        icon: <SecurityIcon />
      },
      document_training: {
        name: 'Custom document training for AI responses',
        benefits: ['Personalized AI responses', 'Brand-specific knowledge', 'Improved accuracy'],
        icon: <AssessmentIcon />
      },
      brand_voice_consistency: {
        name: 'Brand voice consistency across all AI responses',
        benefits: ['Unified brand messaging', 'Professional communication', 'Brand recognition'],
        icon: <StarIcon />
      },
      white_label_ai_responses: {
        name: 'White-label branding for AI responses',
        benefits: ['Custom branding', 'Professional appearance', 'Client-ready solutions'],
        icon: <LaunchIcon />
      },
      multi_language_ai_support: {
        name: 'Multi-language AI response support',
        benefits: ['Global audience reach', 'Localized communication', 'Expanded market access'],
        icon: <AnalyticsIcon />
      },
      advanced_scheduling: {
        name: 'Advanced scheduling and automation features',
        benefits: ['Optimized posting times', 'Automated workflows', 'Increased efficiency'],
        icon: <ScheduleIcon />
      },
      team_collaboration: {
        name: 'Team collaboration and multi-user access',
        benefits: ['Team coordination', 'Role-based permissions', 'Collaborative workflows'],
        icon: <AnalyticsIcon />
      },
      ab_testing: {
        name: 'A/B testing for content optimization',
        benefits: ['Data-driven decisions', 'Performance optimization', 'Higher engagement rates'],
        icon: <AssessmentIcon />
      }
    };
    return descriptions[feature] || {
      name: 'This feature',
      benefits: ['Enhanced functionality'],
      icon: <InfoIcon />
    };
  }, [feature]);

  // Enhanced access granted - render children with optional analytics tracking
  if (hasFullAccess) {
    // Track successful access if analytics enabled
    useEffect(() => {
      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack({
          event: 'feature_access_granted',
          feature,
          action,
          timestamp: new Date().toISOString(),
          accessType: temporaryAccessActive ? 'temporary' : previewActive ? 'preview' : 'full'
        });
      }
    }, [enableAnalytics, onAnalyticsTrack, feature, action, temporaryAccessActive, previewActive]);

    return (
      <Box>
        {/* Temporary access indicator */}
        {temporaryAccessActive && temporaryTimeRemaining > 0 && (
          <Alert
            severity="info"
            sx={{
              mb: 2,
              ...glassMorphismStyles,
              borderColor: ACE_COLORS.YELLOW
            }}
          >
            <AlertTitle>Temporary Access Active</AlertTitle>
            Time remaining: {Math.ceil(temporaryTimeRemaining / 60)} minutes
          </Alert>
        )}

        {/* Preview mode indicator */}
        {previewActive && (
          <Alert
            severity="info"
            sx={{
              mb: 2,
              ...glassMorphismStyles,
              borderColor: ACE_COLORS.PURPLE
            }}
          >
            <AlertTitle>Preview Mode</AlertTitle>
            You are previewing this feature. Upgrade to unlock full access.
          </Alert>
        )}

        {children}
      </Box>
    );
  }

  // Enhanced access denied callback with detailed information
  useEffect(() => {
    if (!hasFullAccess && onAccessDenied) {
      const denialInfo = {
        feature,
        action,
        hasFeatureAccess: hasFeatureAccess_,
        canPerformAction: canPerformAction_,
        hasRoleAccess,
        needsUpgrade,
        trialActive,
        denialReason: getDenialReason(),
        recommendedPlan,
        usageInfo: getUsageInfo(),
        timestamp: new Date().toISOString()
      };

      onAccessDenied(denialInfo);
      handleTrackAccessAttempt();
    }
  }, [
    hasFullAccess,
    onAccessDenied,
    feature,
    action,
    hasFeatureAccess_,
    canPerformAction_,
    hasRoleAccess,
    needsUpgrade,
    trialActive,
    getDenialReason,
    recommendedPlan,
    getUsageInfo,
    handleTrackAccessAttempt
  ]);

  // Enhanced fallback handling
  if (fallback) {
    return (
      <Box sx={{ position: 'relative' }}>
        {fallback}
        {/* Optional upgrade overlay for custom fallbacks */}
        {showUpgrade && needsUpgrade && (
          <Box sx={{
            position: 'absolute',
            top: 0,
            right: 0,
            p: 1
          }}>
            <Tooltip title="Upgrade to unlock this feature">
              <IconButton
                size="small"
                onClick={() => setShowUpgradeModal(true)}
                sx={{
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                  color: ACE_COLORS.PURPLE,
                  '&:hover': {
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.2)
                  }
                }}
              >
                <UpgradeIcon />
              </IconButton>
            </Tooltip>
          </Box>
        )}
      </Box>
    );
  }

  // Enhanced upgrade process with comprehensive error handling
  const handleUpgrade = useCallback(async (planId) => {
    if (onUpgradeClick) {
      onUpgradeClick(planId);
      return;
    }

    try {
      setUpgradeInProgress(true);
      setError(null);

      await upgradeSubscription(planId, { immediate: true });

      showSuccessNotification(`Successfully upgraded to ${SUBSCRIPTION_PLANS[planId]?.name || planId}`);
      setShowUpgradeModal(false);
      announceToScreenReader(`Successfully upgraded to ${SUBSCRIPTION_PLANS[planId]?.name || planId} plan`);

      // Track upgrade analytics
      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack({
          event: 'subscription_upgrade',
          fromFeatureGate: true,
          feature,
          action,
          planId,
          timestamp: new Date().toISOString()
        });
      }
    } catch (err) {
      const errorMessage = err.message || 'Failed to upgrade subscription';
      setError(errorMessage);
      announceToScreenReader(`Upgrade failed: ${errorMessage}`);
    } finally {
      setUpgradeInProgress(false);
    }
  }, [
    onUpgradeClick,
    upgradeSubscription,
    showSuccessNotification,
    announceToScreenReader,
    enableAnalytics,
    onAnalyticsTrack,
    feature,
    action
  ]);



  // Enhanced render methods for different variants
  const renderGateContent = useCallback(() => {
    const usageInfo = getUsageInfo();
    const featureDesc = getFeatureDescription();

    const message = customMessage ||
      (needsUpgrade ?
        `Upgrade to ${recommendedPlan?.name || 'a higher plan'} to unlock ${featureDesc.name}` :
        `${featureDesc.name} is not available in your current plan`
      );

    const commonProps = {
      message,
      usageInfo,
      featureDesc,
      needsUpgrade,
      recommendedPlan,
      trialActive,
      trialDaysRemaining,
      onUpgrade: handleUpgrade,
      onPreview: enablePreview ? handleStartPreview : null,
      showUpgrade,
      showUsage,
      upgradeInProgress,
      error
    };



  // Advanced upgrade modal component
  const UpgradeModal = () => {
    const featureInfo = getFeatureDescription();
    const plans = [
      {
        id: 'creator',
        name: 'Creator',
        price: '$19',
        period: 'month',
        features: ['50 monthly posts', '100 AI auto-replies', '10 regeneration credits'],
        popular: false
      },
      {
        id: 'accelerator',
        name: 'Accelerator',
        price: '$99',
        period: 'month',
        features: ['200 monthly posts', '500 AI auto-replies', '50 regeneration credits', 'Team collaboration'],
        popular: true
      },
      {
        id: 'dominator',
        name: 'Dominator',
        price: '$249',
        period: 'month',
        features: ['Unlimited posts', 'Unlimited AI auto-replies', 'Unlimited regeneration credits', 'White-label branding'],
        popular: false
      }
    ];

    return (
      <Dialog
        open={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6">Upgrade Required</Typography>
            <IconButton onClick={() => setShowUpgradeModal(false)}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent>
          <Box sx={{ mb: 3 }}>
            <Alert severity="info" icon={<InfoIcon />} sx={{ mb: 2 }}>
              {featureInfo.name} requires a higher subscription plan.
            </Alert>

            {/* Feature Benefits */}
            <Typography variant="subtitle2" gutterBottom>
              What you'll get:
            </Typography>
            <List dense>
              {featureInfo.benefits.map((benefit, index) => (
                <ListItem key={index} sx={{ py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 32 }}>
                    <CheckCircleIcon color="success" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary={benefit} />
                </ListItem>
              ))}
            </List>
          </Box>

          {/* Trial Information */}
          {trialActive && showTrialPrompt && (
            <Alert severity="warning" icon={<TimerIcon />} sx={{ mb: 3 }}>
              Your trial ends in {trialDaysRemaining} days. Upgrade now to continue using all features.
            </Alert>
          )}

          {/* Plan Options */}
          <Typography variant="h6" gutterBottom>
            Choose Your Plan
          </Typography>
          <Stack spacing={2}>
            {plans.map((plan) => (
              <Card
                key={plan.id}
                variant={plan.popular ? "elevation" : "outlined"}
                sx={{
                  position: 'relative',
                  border: plan.popular ? '2px solid' : undefined,
                  borderColor: plan.popular ? 'primary.main' : undefined
                }}
              >
                {plan.popular && (
                  <Chip
                    label="Most Popular"
                    color="primary"
                    size="small"
                    sx={{
                      position: 'absolute',
                      top: -8,
                      left: 16,
                      zIndex: 1
                    }}
                  />
                )}
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box>
                      <Typography variant="h6">{plan.name}</Typography>
                      <Typography variant="h4" color="primary">
                        {plan.price}
                        <Typography component="span" variant="body2" color="text.secondary">
                          /{plan.period}
                        </Typography>
                      </Typography>
                    </Box>
                    <Button
                      variant={plan.popular ? "contained" : "outlined"}
                      onClick={() => handleUpgrade(plan.id)}
                      disabled={upgradeInProgress}
                      startIcon={upgradeInProgress ? <Timer /> : <UpgradeIcon />}
                    >
                      {upgradeInProgress ? 'Upgrading...' : 'Upgrade'}
                    </Button>
                  </Box>
                  <List dense sx={{ mt: 1 }}>
                    {plan.features.map((feature, index) => (
                      <ListItem key={index} sx={{ py: 0, px: 0 }}>
                        <ListItemIcon sx={{ minWidth: 24 }}>
                          <CheckCircleIcon color="success" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText primary={feature} />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            ))}
          </Stack>
        </DialogContent>

        <DialogActions>
          <Button onClick={() => setShowUpgradeModal(false)}>
            Cancel
          </Button>
          <Button onClick={() => navigate('/billing')} variant="outlined">
            View All Plans
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Render upgrade prompt based on variant
  const renderUpgradePrompt = () => {
    const featureInfo = getFeatureDescription();

    // Determine the appropriate message based on access denial reason
    let message = customMessage;
    let severity = 'warning';
    let icon = <LockIcon />;

    if (!message) {
      if (!hasRoleAccess) {
        message = `You don't have the required permissions to access ${featureInfo.name}.`;
        severity = 'error';
        icon = <SecurityIcon />;
      } else if (needsUpgrade) {
        message = `${featureInfo.name} is not available in your current plan.`;
        severity = 'info';
        icon = <UpgradeIcon />;
      } else if (!canPerformAction_) {
        message = `You've reached your monthly limit for this feature.`;
        severity = 'warning';
        icon = <SpeedIcon />;
      } else {
        message = `Access to ${featureInfo.name} is currently restricted.`;
      }
    }

    if (variant === 'inline') {
      return (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, p: 1 }}>
          {icon}
          <Typography variant="body2" color="text.secondary">
            {message}
          </Typography>
          {showUpgrade && !hasRoleAccess && (
            <Tooltip title="Contact your administrator for access">
              <Button size="small" variant="outlined" disabled>
                Contact Admin
              </Button>
            </Tooltip>
          )}
          {showUpgrade && hasRoleAccess && planInfo && (
            <Button
              size="small"
              variant="outlined"
              onClick={() => setShowUpgradeModal(true)}
            >
              Upgrade
            </Button>
          )}
        </Box>
      );
    }

    if (variant === 'alert') {
      return (
        <Alert
          severity={severity}
          icon={icon}
          action={
            showUpgrade && hasRoleAccess && planInfo ? (
              <Button
                color="inherit"
                size="small"
                onClick={() => setShowUpgradeModal(true)}
              >
                Upgrade
              </Button>
            ) : !hasRoleAccess ? (
              <Button
                color="inherit"
                size="small"
                onClick={() => navigate('/support')}
              >
                Contact Support
              </Button>
            ) : null
          }
        >
          {message}
          {trialActive && showTrialPrompt && (
            <Typography variant="body2" sx={{ mt: 1 }}>
              Trial ends in {trialDaysRemaining} days.
            </Typography>
          )}
        </Alert>
      );
    }

    if (variant === 'modal') {
      return (
        <>
          <Button
            variant="outlined"
            startIcon={icon}
            onClick={() => setShowUpgradeModal(true)}
            disabled={!hasRoleAccess}
          >
            {!hasRoleAccess ? 'Access Denied' : 'Upgrade Required'}
          </Button>
          <UpgradeModal />
        </>
      );
    }

    // Default card variant
    return (
      <Card sx={{ maxWidth: 400, mx: 'auto' }}>
        <CardContent sx={{ textAlign: 'center', p: 3 }}>
          <Box sx={{ mb: 2 }}>
            <LockIcon sx={{ fontSize: 48, color: 'warning.main', mb: 1 }} />
            <Typography variant="h6" gutterBottom>
              {needsUpgrade ? 'Feature Not Available' : 'Usage Limit Reached'}
            </Typography>
          </Box>

          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {message}
          </Typography>

          {/* Current Plan Info */}
          <Box sx={{ mb: 2 }}>
            <Chip
              label={`Current: ${subscription?.plan_name || 'Explorer'}`}
              size="small"
              variant="outlined"
            />
          </Box>

          {/* Usage Information */}
          {showUsage && usageInfo && !needsUpgrade && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="body2" gutterBottom>
                Monthly Usage
              </Typography>
              <LinearProgress
                variant="determinate"
                value={usageInfo.percentage}
                sx={{ mb: 1, height: 8, borderRadius: 4 }}
                color={usageInfo.percentage > 90 ? 'error' : 'primary'}
              />
              <Typography variant="caption" color="text.secondary">
                {usageInfo.remaining} remaining this month
              </Typography>
            </Box>
          )}

          {/* Role-based Access Message */}
          {!hasRoleAccess && (
            <Alert severity="error" icon={<SecurityIcon />} sx={{ mb: 2 }}>
              You don't have the required permissions. Contact your administrator.
            </Alert>
          )}

          {/* Trial Information */}
          {trialActive && showTrialPrompt && hasRoleAccess && (
            <Alert severity="warning" icon={<TimerIcon />} sx={{ mb: 2 }}>
              Trial ends in {trialDaysRemaining} days. Upgrade to continue using all features.
            </Alert>
          )}

          {/* Upgrade Options */}
          {showUpgrade && hasRoleAccess && (
            <Stack spacing={2}>
              <Divider />

              {planInfo && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Recommended Upgrade
                  </Typography>
                  <Button
                    variant="contained"
                    fullWidth
                    startIcon={<UpgradeIcon />}
                    onClick={() => setShowUpgradeModal(true)}
                    sx={{ mb: 1 }}
                  >
                    Upgrade to {planInfo.name}
                  </Button>
                  <Typography variant="caption" color="text.secondary">
                    {planInfo.price}
                  </Typography>
                </Box>
              )}

              {!needsUpgrade && (
                <Button
                  variant="outlined"
                  fullWidth
                  startIcon={<TrendingUpIcon />}
                  onClick={() => navigate('/billing/addons')}
                >
                  Buy Additional Credits
                </Button>
              )}
            </Stack>
          )}

          {/* Support Contact for Role Issues */}
          {!hasRoleAccess && (
            <Button
              variant="outlined"
              fullWidth
              startIcon={<SecurityIcon />}
              onClick={() => navigate('/support')}
              sx={{ mt: 2 }}
            >
              Contact Support
            </Button>
          )}
        </CardContent>
      </Card>
    );
  };

  // Enhanced render gate content
  const renderGateContent = () => {
    const usageInfo = getUsageInfo();
    const featureDesc = getFeatureDescription();

    const message = customMessage ||
      (needsUpgrade ?
        `Upgrade to ${recommendedPlan?.name || 'a higher plan'} to unlock ${featureDesc.name}` :
        `${featureDesc.name} is not available in your current plan`
      );

    // Default card variant with enhanced styling
    return (
      <Card sx={{
        ...glassMorphismStyles,
        border: `2px solid ${alpha(ACE_COLORS.PURPLE, 0.3)}`,
        maxWidth: 400,
        mx: 'auto'
      }}>
        <CardContent sx={{ textAlign: 'center', p: 3 }}>
          <Box sx={{ mb: 2 }}>
            <LockIcon sx={{
              fontSize: 48,
              color: ACE_COLORS.PURPLE,
              mb: 1
            }} />
            <Typography variant="h6" gutterBottom sx={{
              fontWeight: 600,
              color: ACE_COLORS.DARK
            }}>
              Feature Locked
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {message}
            </Typography>
          </Box>

          {/* Feature benefits */}
          {featureDesc.benefits && (
            <Box sx={{ mb: 3, textAlign: 'left' }}>
              <Typography variant="body2" sx={{ mb: 1, fontWeight: 500 }}>
                Unlock these benefits:
              </Typography>
              <List dense>
                {featureDesc.benefits.slice(0, 3).map((benefit, index) => (
                  <ListItem key={index} sx={{ py: 0.5, px: 0 }}>
                    <ListItemIcon sx={{ minWidth: 32 }}>
                      <CheckCircleIcon sx={{
                        fontSize: 16,
                        color: ACE_COLORS.PURPLE
                      }} />
                    </ListItemIcon>
                    <ListItemText
                      primary={benefit}
                      primaryTypographyProps={{
                        variant: 'body2',
                        color: 'text.secondary'
                      }}
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}

          {/* Usage information */}
          {showUsage && usageInfo && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Current Usage: {usageInfo.percentage?.toFixed(1) || 0}%
              </Typography>
              <LinearProgress
                variant="determinate"
                value={Math.min(usageInfo.percentage || 0, 100)}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: ACE_COLORS.PURPLE
                  }
                }}
              />
            </Box>
          )}

          {/* Trial information */}
          {trialActive && showTrialPrompt && (
            <Alert
              severity="info"
              sx={{
                mb: 2,
                textAlign: 'left',
                '& .MuiAlert-icon': {
                  color: ACE_COLORS.PURPLE
                }
              }}
            >
              <Typography variant="body2">
                {trialDaysRemaining > 0
                  ? `${trialDaysRemaining} days left in trial`
                  : 'Trial expired'
                }
              </Typography>
            </Alert>
          )}

          {/* Action buttons */}
          {showUpgrade && needsUpgrade && (
            <Button
              variant="contained"
              startIcon={upgradeInProgress ? <CircularProgress size={16} /> : <UpgradeIcon />}
              onClick={() => setShowUpgradeModal(true)}
              disabled={upgradeInProgress}
              sx={{
                backgroundColor: ACE_COLORS.PURPLE,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8)
                }
              }}
            >
              {upgradeInProgress ? 'Upgrading...' : `Upgrade to ${recommendedPlan?.name || 'Pro'}`}
            </Button>
          )}

          {/* Preview button */}
          {enablePreview && !previewActive && (
            <Button
              variant="outlined"
              startIcon={<VisibilityIcon />}
              onClick={handleStartPreview}
              sx={{
                ml: 1,
                borderColor: ACE_COLORS.PURPLE,
                color: ACE_COLORS.PURPLE,
                '&:hover': {
                  borderColor: ACE_COLORS.PURPLE,
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                }
              }}
            >
              Preview
            </Button>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <>
      {renderGateContent()}
      <UpgradeModal />
    </>
  );
}));

FeatureGate.displayName = 'FeatureGate';

FeatureGate.propTypes = {
  /** Feature name to check access for */
  feature: PropTypes.string,
  /** Action name to check access for */
  action: PropTypes.string,
  /** Quantity required for the action */
  quantity: PropTypes.number,
  /** Children to render when access is granted */
  children: PropTypes.node,
  /** Fallback content to render when access is denied */
  fallback: PropTypes.node,
  /** Whether to show upgrade options */
  showUpgrade: PropTypes.bool,
  /** Whether to show usage information */
  showUsage: PropTypes.bool,
  /** Custom message to display */
  customMessage: PropTypes.string,
  /** Visual variant of the gate */
  variant: PropTypes.oneOf(['card', 'inline', 'alert', 'modal', 'banner', 'tooltip']),
  /** Required role for access */
  requiresRole: PropTypes.string,
  /** Whether to show trial prompts */
  showTrialPrompt: PropTypes.bool,
  /** Enable real-time updates */
  enableRealTimeUpdates: PropTypes.bool,
  /** Enable analytics tracking */
  enableAnalytics: PropTypes.bool,
  /** Enable feature preview */
  enablePreview: PropTypes.bool,
  /** Enable temporary access */
  temporaryAccess: PropTypes.bool,
  /** Temporary access duration in seconds */
  temporaryDuration: PropTypes.number,
  /** Function called when upgrade is clicked */
  onUpgradeClick: PropTypes.func,
  /** Function called when access is denied */
  onAccessDenied: PropTypes.func,
  /** Function called for feature preview */
  onFeaturePreview: PropTypes.func,
  /** Function called for analytics tracking */
  onAnalyticsTrack: PropTypes.func,
  /** Function called when temporary access expires */
  onTemporaryAccessExpired: PropTypes.func
};

// Usage indicator component
export const UsageIndicator = ({ feature, showLabel = true, size = 'medium' }) => {
  const { getRemainingUsage, getUsagePercentage, featureLimits } = useSubscription();

  const remaining = getRemainingUsage(feature);
  const percentage = getUsagePercentage(feature);
  const limit = featureLimits?.[feature] || 0;
  const used = limit - remaining;

  const getColor = () => {
    if (percentage > 90) return 'error';
    if (percentage > 75) return 'warning';
    return 'primary';
  };

  const getSize = () => {
    switch (size) {
      case 'small': return { height: 4, fontSize: '0.75rem' };
      case 'large': return { height: 12, fontSize: '1rem' };
      default: return { height: 8, fontSize: '0.875rem' };
    }
  };

  const sizeProps = getSize();

  return (
    <Box sx={{ minWidth: 120 }}>
      {showLabel && (
        <Typography variant="caption" color="text.secondary" sx={{ fontSize: sizeProps.fontSize }}>
          {used} / {limit === 999999 ? '∞' : limit} used
        </Typography>
      )}
      <LinearProgress
        variant="determinate"
        value={percentage}
        color={getColor()}
        sx={{
          height: sizeProps.height,
          borderRadius: sizeProps.height / 2,
          mt: showLabel ? 0.5 : 0
        }}
      />
    </Box>
  );
};

export default FeatureGate;