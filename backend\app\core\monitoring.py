"""
Comprehensive monitoring and observability setup for production deployment.

This module provides metrics collection, health checks, performance monitoring,
and integration with external monitoring services like Prometheus, Sentry, and APM.
"""
import asyncio
import logging
import time
import platform
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from dataclasses import dataclass, asdict
from contextlib import asynccontextmanager

# Optional dependencies
try:
    import psutil
except ImportError:
    psutil = None

try:
    import sentry_sdk  # type: ignore
    from sentry_sdk.integrations.fastapi import FastApiIntegration  # type: ignore
    from sentry_sdk.integrations.asyncio import AsyncioIntegration  # type: ignore
    from sentry_sdk.integrations.logging import LoggingIntegration  # type: ignore
except ImportError:
    sentry_sdk = None
    FastApiIntegration = None
    AsyncioIntegration = None
    LoggingIntegration = None

try:
    from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry, generate_latest, CONTENT_TYPE_LATEST  # type: ignore
except ImportError:
    # Create mock classes when prometheus_client is not available
    class MockMetric:
        def __init__(self, *args, **kwargs):
            pass
        def labels(self, **kwargs):
            return self
        def inc(self, *args, **kwargs):
            pass
        def observe(self, *args, **kwargs):
            pass
        def set(self, *args, **kwargs):
            pass
        def time(self):
            return self
        def __enter__(self):
            return self
        def __exit__(self, *args):
            pass

    Counter = MockMetric
    Histogram = MockMetric
    Gauge = MockMetric
    CollectorRegistry = MockMetric
    generate_latest = lambda x: b"# Prometheus not available"
    CONTENT_TYPE_LATEST = "text/plain"

from app.core.config import settings
from app.core.redis import redis_manager
from app.db.mongodb import mongodb

# Set up logging
logger = logging.getLogger(__name__)

# Initialize Prometheus metrics
def _init_prometheus_metrics():
    """Initialize Prometheus metrics."""
    registry = CollectorRegistry()

    return {
        'REGISTRY': registry,
        'REQUEST_COUNT': Counter(
            'http_requests_total',
            'Total HTTP requests',
            ['method', 'endpoint', 'status_code'],
            registry=registry
        ),
        'REQUEST_DURATION': Histogram(
            'http_request_duration_seconds',
            'HTTP request duration in seconds',
            ['method', 'endpoint'],
            registry=registry
        ),
        'ACTIVE_CONNECTIONS': Gauge(
            'active_connections_total',
            'Number of active connections',
            registry=registry
        ),
        'DATABASE_CONNECTIONS': Gauge(
            'database_connections_total',
            'Number of database connections',
            registry=registry
        ),
        'REDIS_CONNECTIONS': Gauge(
            'redis_connections_total',
            'Number of Redis connections',
            registry=registry
        )
    }

# Initialize metrics
_metrics = _init_prometheus_metrics()
REGISTRY = _metrics['REGISTRY']
REQUEST_COUNT = _metrics['REQUEST_COUNT']
REQUEST_DURATION = _metrics['REQUEST_DURATION']
ACTIVE_CONNECTIONS = _metrics['ACTIVE_CONNECTIONS']
DATABASE_CONNECTIONS = _metrics['DATABASE_CONNECTIONS']
REDIS_CONNECTIONS = _metrics['REDIS_CONNECTIONS']

# Business metrics
UNIFIED_INBOX_OPERATIONS = Counter(
    'unified_inbox_operations_total',
    'Total unified inbox operations',
    ['operation_type', 'platform', 'status'],
    registry=REGISTRY
)

CACHE_OPERATIONS = Counter(
    'cache_operations_total',
    'Total cache operations',
    ['operation', 'cache_type', 'status'],
    registry=REGISTRY
)

ERROR_COUNT = Counter(
    'application_errors_total',
    'Total application errors',
    ['error_type', 'severity'],
    registry=REGISTRY
)

# Sentiment Analysis metrics
SENTIMENT_ANALYSIS_COUNT = Counter(
    'sentiment_analysis_total',
    'Total sentiment analyses performed',
    ['subscription_tier', 'intent_category', 'status'],
    registry=REGISTRY
)

SENTIMENT_ANALYSIS_DURATION = Histogram(
    'sentiment_analysis_duration_seconds',
    'Time spent on sentiment analysis',
    ['subscription_tier', 'operation_type'],
    registry=REGISTRY,
    buckets=(0.05, 0.1, 0.2, 0.5, 1.0, 2.0, 5.0, 10.0)
)

SENTIMENT_HEALTH_SCORE = Gauge(
    'sentiment_health_score',
    'Overall customer sentiment health score',
    registry=REGISTRY
)

ACTIVE_CHURN_RISKS = Gauge(
    'active_churn_risks',
    'Number of customers at churn risk',
    registry=REGISTRY
)

URGENT_CONVERSATIONS = Gauge(
    'urgent_conversations',
    'Number of conversations requiring urgent attention',
    registry=REGISTRY
)

# Response Suggestion metrics
RESPONSE_SUGGESTIONS_COUNT = Counter(
    'response_suggestions_total',
    'Total response suggestions generated',
    ['subscription_tier', 'suggestion_type', 'status'],
    registry=REGISTRY
)

RESPONSE_SUGGESTIONS_DURATION = Histogram(
    'response_suggestions_duration_seconds',
    'Time spent generating response suggestions',
    ['subscription_tier', 'ai_service_status'],
    registry=REGISTRY,
    buckets=(0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0)
)

RESPONSE_QUALITY_SCORE = Histogram(
    'response_quality_score',
    'Quality scores of generated responses',
    ['subscription_tier', 'customer_intent'],
    registry=REGISTRY,
    buckets=(1, 2, 3, 4, 5, 6, 7, 8, 9, 10)
)

AI_SERVICE_AVAILABILITY = Gauge(
    'ai_service_availability',
    'AI service availability status (1=available, 0=unavailable)',
    ['service_name'],
    registry=REGISTRY
)

CACHE_HIT_RATE = Gauge(
    'cache_hit_rate',
    'Cache hit rate for various operations',
    ['operation_type'],
    registry=REGISTRY
)

# Add-on specific metrics
ADDON_PURCHASES = Counter(
    'addon_purchases_total',
    'Total add-on purchases',
    ['addon_id', 'variant', 'subscription_tier'],
    registry=REGISTRY
)

ADDON_USAGE = Counter(
    'addon_usage_total',
    'Total add-on usage events',
    ['addon_id', 'usage_type', 'subscription_tier'],
    registry=REGISTRY
)

ADDON_CREDITS_REMAINING = Gauge(
    'addon_credits_remaining',
    'Remaining credits for add-ons',
    ['addon_id', 'user_id'],
    registry=REGISTRY
)

ADDON_REVENUE = Counter(
    'addon_revenue_total',
    'Total revenue from add-ons',
    ['addon_id', 'variant'],
    registry=REGISTRY
)

ADDON_RECOMMENDATIONS_SHOWN = Counter(
    'addon_recommendations_shown_total',
    'Total add-on recommendations shown',
    ['addon_id', 'context', 'subscription_tier'],
    registry=REGISTRY
)

ADDON_CONVERSION_RATE = Gauge(
    'addon_conversion_rate',
    'Add-on conversion rate by type',
    ['addon_category', 'subscription_tier'],
    registry=REGISTRY
)

FEATURE_ACCESS_ATTEMPTS = Counter(
    'feature_access_attempts_total',
    'Total feature access attempts',
    ['feature', 'result', 'subscription_tier'],
    registry=REGISTRY
)

UPSELL_NOTIFICATIONS_SENT = Counter(
    'upsell_notifications_sent_total',
    'Total upsell notifications sent',
    ['trigger_type', 'addon_id', 'subscription_tier'],
    registry=REGISTRY
)

@dataclass
class HealthStatus:
    """Health status data structure."""
    service: str
    status: str  # healthy, degraded, unhealthy
    response_time_ms: float
    details: Dict[str, Any]
    timestamp: datetime

@dataclass
class SystemMetrics:
    """System metrics data structure."""
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    network_io: Dict[str, int]
    process_count: int
    uptime_seconds: float
    timestamp: datetime

class MonitoringManager:
    """
    Comprehensive monitoring manager for production deployment.
    """

    def __init__(self):
        self.start_time = time.time()
        self.health_checks = {}
        self.metrics_collection_task = None
        self.is_monitoring_active = False

        # Initialize Prometheus gauges
        self.cpu_gauge = Gauge('system_cpu_percent', 'System CPU usage percentage', registry=REGISTRY)
        self.disk_gauge = Gauge('system_disk_percent', 'System disk usage percentage', registry=REGISTRY)

    async def initialize(self):
        """Initialize monitoring services."""
        try:
            # Initialize Sentry for error tracking
            if getattr(settings, 'SENTRY_ENABLED', False):
                self._setup_sentry()

            # Start metrics collection
            if getattr(settings, 'ENABLE_METRICS', False):
                self.metrics_collection_task = asyncio.create_task(self._metrics_collection_loop())

            self.is_monitoring_active = True
            logger.info("Monitoring services initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize monitoring: {str(e)}")
            raise

    def _setup_sentry(self):
        """Setup Sentry error tracking."""

        integrations = []
        if FastApiIntegration:
            integrations.append(FastApiIntegration(auto_enabling_integrations=False))
        if AsyncioIntegration:
            integrations.append(AsyncioIntegration())
        if LoggingIntegration:
            integrations.append(LoggingIntegration(level=logging.INFO, event_level=logging.ERROR))

        if sentry_sdk and hasattr(sentry_sdk, 'init'):
            sentry_sdk.init(
                dsn=getattr(settings, 'SENTRY_DSN', ''),
                environment=getattr(settings, 'SENTRY_ENVIRONMENT', 'production'),
                release=getattr(settings, 'SENTRY_RELEASE', '1.0.0'),
            traces_sample_rate=getattr(settings, 'SENTRY_TRACES_SAMPLE_RATE', 0.1),
            integrations=integrations,
            before_send=self._sentry_before_send,
            attach_stacktrace=True,
            send_default_pii=False
        )
        logger.info("Sentry error tracking initialized")

    def _sentry_before_send(self, event, hint):
        """Filter and modify Sentry events before sending."""
        # Filter out health check requests
        if 'request' in event and event['request'].get('url', '').endswith('/health'):
            return None

        # Add custom context
        event.setdefault('extra', {}).update({
            'instance_id': getattr(settings, 'INSTANCE_ID', 'unknown'),
            'deployment_version': getattr(settings, 'DEPLOYMENT_VERSION', 'unknown'),
            'has_hint': hint is not None  # Use hint parameter
        })

        return event

    async def _metrics_collection_loop(self):
        """Continuous metrics collection loop."""
        while self.is_monitoring_active:
            try:
                await self._collect_system_metrics()
                await self._collect_application_metrics()
                await asyncio.sleep(30)  # Collect metrics every 30 seconds

            except Exception as e:
                logger.error(f"Error in metrics collection: {str(e)}")
                await asyncio.sleep(60)  # Wait longer on error

    async def _collect_system_metrics(self):
        """Collect system-level metrics."""
        try:
            if not psutil:
                logger.debug("psutil not available, skipping system metrics collection")
                return

            # CPU and disk metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            disk = psutil.disk_usage('/')

            # Network I/O
            network_io = psutil.net_io_counters()._asdict()
            logger.debug(f"Network I/O stats: {network_io}")

            # Process information
            process_count = len(psutil.pids())
            logger.debug(f"Process count: {process_count}")

            # Update Prometheus gauges if available
            if self.cpu_gauge:
                self.cpu_gauge.set(cpu_percent)
            if self.disk_gauge:
                self.disk_gauge.set(disk.percent)

            logger.debug(f"System metrics collected: CPU={cpu_percent}%, Disk={disk.percent}%")

        except Exception as e:
            logger.error(f"Error collecting system metrics: {str(e)}")

    async def _collect_application_metrics(self):
        """Collect application-specific metrics."""
        try:
            # Database connection metrics
            if mongodb.is_connected:
                DATABASE_CONNECTIONS.set(1)
            else:
                DATABASE_CONNECTIONS.set(0)

            # Redis connection metrics
            if redis_manager.is_connected:
                REDIS_CONNECTIONS.set(1)
                redis_stats = await redis_manager.get_stats()
                if redis_stats.get('redis_info'):
                    connected_clients = redis_stats['redis_info'].get('connected_clients', 0)
                    ACTIVE_CONNECTIONS.set(connected_clients)
            else:
                REDIS_CONNECTIONS.set(0)

            logger.debug("Application metrics collected")

        except Exception as e:
            logger.error(f"Error collecting application metrics: {str(e)}")

    async def check_health(self, detailed: bool = False) -> Dict[str, Any]:
        """
        Perform comprehensive health checks.

        Args:
            detailed: Whether to include detailed health information

        Returns:
            Health check results
        """
        health_results = {
            "status": "healthy",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "uptime_seconds": time.time() - self.start_time,
            "version": getattr(settings, 'DEPLOYMENT_VERSION', 'unknown'),
            "instance_id": getattr(settings, 'INSTANCE_ID', 'unknown'),
            "checks": {}
        }

        # Database health check
        db_health = await self._check_database_health()
        health_results["checks"]["database"] = asdict(db_health)

        # Redis health check
        redis_health = await self._check_redis_health()
        health_results["checks"]["redis"] = asdict(redis_health)

        # System health check
        if detailed:
            system_health = await self._check_system_health()
            health_results["checks"]["system"] = asdict(system_health)

        # Determine overall status
        statuses = [check["status"] for check in health_results["checks"].values()]
        if "unhealthy" in statuses:
            health_results["status"] = "unhealthy"
        elif "degraded" in statuses:
            health_results["status"] = "degraded"

        return health_results

    async def _check_database_health(self) -> HealthStatus:
        """Check database health."""
        start_time = time.time()

        try:
            if mongodb.is_connected:
                # Test database operation
                db = mongodb.db
                if db is not None:
                    await db.command("ping")
                else:
                    raise Exception("Database connection is None")

                response_time = (time.time() - start_time) * 1000

                return HealthStatus(
                    service="database",
                    status="healthy",
                    response_time_ms=response_time,
                    details={
                        "connection_status": "connected",
                        "database_name": settings.MONGODB_DB_NAME
                    },
                    timestamp=datetime.now(timezone.utc)
                )
            else:
                return HealthStatus(
                    service="database",
                    status="unhealthy",
                    response_time_ms=0,
                    details={"connection_status": "disconnected"},
                    timestamp=datetime.now(timezone.utc)
                )

        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthStatus(
                service="database",
                status="unhealthy",
                response_time_ms=response_time,
                details={"error": str(e)},
                timestamp=datetime.now(timezone.utc)
            )

    async def _check_redis_health(self) -> HealthStatus:
        """Check Redis health."""
        start_time = time.time()

        try:
            if redis_manager.is_connected:
                # Test Redis operation using execute_with_retry
                async def redis_ping(client):
                    await client.ping()
                    return True

                await redis_manager.execute_with_retry(redis_ping, client_type="primary")
                response_time = (time.time() - start_time) * 1000
                stats = await redis_manager.get_stats()

                return HealthStatus(
                    service="redis",
                    status="healthy",
                    response_time_ms=response_time,
                    details={
                        "connection_status": "connected",
                        "stats": stats
                    },
                    timestamp=datetime.now(timezone.utc)
                )
            else:
                return HealthStatus(
                    service="redis",
                    status="degraded",  # Redis is optional
                    response_time_ms=0,
                    details={"connection_status": "disconnected"},
                    timestamp=datetime.now(timezone.utc)
                )

        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthStatus(
                service="redis",
                status="degraded",
                response_time_ms=response_time,
                details={"error": str(e)},
                timestamp=datetime.now(timezone.utc)
            )

    async def _check_system_health(self) -> HealthStatus:
        """Check system health."""
        start_time = time.time()

        try:
            # Collect system metrics if psutil is available
            cpu_percent = 0
            disk_percent = 0

            if psutil:
                cpu_percent = psutil.cpu_percent(interval=0.1)
                disk = psutil.disk_usage('/')
                disk_percent = disk.percent

            # Determine status based on thresholds
            status = "healthy"
            if cpu_percent > 90 or disk_percent > 90:
                status = "degraded"
            if cpu_percent > 95 or disk_percent > 95:
                status = "unhealthy"

            response_time = (time.time() - start_time) * 1000

            return HealthStatus(
                service="system",
                status=status,
                response_time_ms=response_time,
                details={
                    "cpu_percent": cpu_percent,
                    "disk_percent": disk.percent,
                    "platform": platform.platform(),
                    "python_version": platform.python_version()
                },
                timestamp=datetime.now(timezone.utc)
            )

        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthStatus(
                service="system",
                status="unhealthy",
                response_time_ms=response_time,
                details={"error": str(e)},
                timestamp=datetime.now(timezone.utc)
            )

    def record_request(self, method: str, endpoint: str, status_code: int, duration: float):
        """Record HTTP request metrics."""
        REQUEST_COUNT.labels(method=method, endpoint=endpoint, status_code=status_code).inc()
        REQUEST_DURATION.labels(method=method, endpoint=endpoint).observe(duration)

    def record_inbox_operation(self, operation_type: str, platform: str, status: str):
        """Record unified inbox operation metrics."""
        UNIFIED_INBOX_OPERATIONS.labels(
            operation_type=operation_type,
            platform=platform,
            status=status
        ).inc()

    def record_cache_operation(self, operation: str, cache_type: str, status: str):
        """Record cache operation metrics."""
        CACHE_OPERATIONS.labels(
            operation=operation,
            cache_type=cache_type,
            status=status
        ).inc()

    def record_error(self, error_type: str, severity: str):
        """Record application error metrics."""
        ERROR_COUNT.labels(error_type=error_type, severity=severity).inc()

    def record_sentiment_analysis(self, subscription_tier: str, intent_category: str,
                                 status: str, duration: float, operation_type: str = "single"):
        """Record sentiment analysis metrics."""
        SENTIMENT_ANALYSIS_COUNT.labels(
            subscription_tier=subscription_tier,
            intent_category=intent_category,
            status=status
        ).inc()

        SENTIMENT_ANALYSIS_DURATION.labels(
            subscription_tier=subscription_tier,
            operation_type=operation_type
        ).observe(duration)

    def update_sentiment_health_metrics(self, health_score: float, churn_risk_count: int, urgent_count: int):
        """Update sentiment health metrics."""
        SENTIMENT_HEALTH_SCORE.set(health_score)
        ACTIVE_CHURN_RISKS.set(churn_risk_count)
        URGENT_CONVERSATIONS.set(urgent_count)

    def record_response_suggestion_metrics(
        self,
        subscription_tier: str,
        suggestion_type: str,
        status: str,
        duration: float,
        ai_service_status: str = "available",
        quality_score: Optional[float] = None,
        customer_intent: str = "unknown"
    ):
        """Record response suggestion metrics."""
        RESPONSE_SUGGESTIONS_COUNT.labels(
            subscription_tier=subscription_tier,
            suggestion_type=suggestion_type,
            status=status
        ).inc()

        RESPONSE_SUGGESTIONS_DURATION.labels(
            subscription_tier=subscription_tier,
            ai_service_status=ai_service_status
        ).observe(duration)

        if quality_score is not None:
            RESPONSE_QUALITY_SCORE.labels(
                subscription_tier=subscription_tier,
                customer_intent=customer_intent
            ).observe(quality_score)

    def update_ai_service_availability(self, service_name: str, is_available: bool):
        """Update AI service availability status."""
        AI_SERVICE_AVAILABILITY.labels(service_name=service_name).set(1 if is_available else 0)

    def update_cache_hit_rate(self, operation_type: str, hit_rate: float):
        """Update cache hit rate metrics."""
        CACHE_HIT_RATE.labels(operation_type=operation_type).set(hit_rate)

    # Add-on specific metrics methods
    def record_addon_purchase(self, addon_id: str, variant: str, subscription_tier: str, revenue: float):
        """Record add-on purchase metrics."""
        ADDON_PURCHASES.labels(
            addon_id=addon_id,
            variant=variant,
            subscription_tier=subscription_tier
        ).inc()

        ADDON_REVENUE.labels(
            addon_id=addon_id,
            variant=variant
        ).inc(int(revenue * 100))  # Convert to cents to avoid float issues

    def record_addon_usage(self, addon_id: str, usage_type: str, subscription_tier: str, amount: int = 1):
        """Record add-on usage metrics."""
        ADDON_USAGE.labels(
            addon_id=addon_id,
            usage_type=usage_type,
            subscription_tier=subscription_tier
        ).inc(amount)

    def update_addon_credits(self, addon_id: str, user_id: str, credits_remaining: int):
        """Update add-on credits remaining."""
        ADDON_CREDITS_REMAINING.labels(
            addon_id=addon_id,
            user_id=str(user_id)
        ).set(credits_remaining)

    def record_addon_recommendation(self, addon_id: str, context: str, subscription_tier: str):
        """Record add-on recommendation shown."""
        ADDON_RECOMMENDATIONS_SHOWN.labels(
            addon_id=addon_id,
            context=context,
            subscription_tier=subscription_tier
        ).inc()

    def update_addon_conversion_rate(self, addon_category: str, subscription_tier: str, rate: float):
        """Update add-on conversion rate."""
        ADDON_CONVERSION_RATE.labels(
            addon_category=addon_category,
            subscription_tier=subscription_tier
        ).set(rate)

    def record_feature_access_attempt(self, feature: str, result: str, subscription_tier: str):
        """Record feature access attempt."""
        FEATURE_ACCESS_ATTEMPTS.labels(
            feature=feature,
            result=result,
            subscription_tier=subscription_tier
        ).inc()

    def record_upsell_notification(self, trigger_type: str, addon_id: str, subscription_tier: str):
        """Record upsell notification sent."""
        UPSELL_NOTIFICATIONS_SENT.labels(
            trigger_type=trigger_type,
            addon_id=addon_id,
            subscription_tier=subscription_tier
        ).inc()

    def get_prometheus_metrics(self) -> str:
        """Get Prometheus metrics in text format."""
        if not generate_latest:
            return "# Prometheus client not available\n"
        return generate_latest(REGISTRY).decode('utf-8')

    async def shutdown(self):
        """Shutdown monitoring services."""
        self.is_monitoring_active = False

        if self.metrics_collection_task:
            self.metrics_collection_task.cancel()
            try:
                await self.metrics_collection_task
            except asyncio.CancelledError:
                pass

        logger.info("Monitoring services shut down")

# Global monitoring manager
monitoring = MonitoringManager()

# Convenience functions
async def initialize_monitoring():
    """Initialize monitoring services."""
    await monitoring.initialize()

async def shutdown_monitoring():
    """Shutdown monitoring services."""
    await monitoring.shutdown()

def record_request_metrics(method: str, endpoint: str, status_code: int, duration: float):
    """Record HTTP request metrics."""
    monitoring.record_request(method, endpoint, status_code, duration)

def record_inbox_metrics(operation_type: str, platform: str, status: str):
    """Record unified inbox operation metrics."""
    monitoring.record_inbox_operation(operation_type, platform, status)

def record_cache_metrics(operation: str, cache_type: str, status: str):
    """Record cache operation metrics."""
    monitoring.record_cache_operation(operation, cache_type, status)

def record_error_metrics(error_type: str, severity: str):
    """Record application error metrics."""
    monitoring.record_error(error_type, severity)

def record_sentiment_metrics(subscription_tier: str, intent_category: str,
                           status: str, duration: float, operation_type: str = "single"):
    """Record sentiment analysis metrics."""
    monitoring.record_sentiment_analysis(subscription_tier, intent_category, status, duration, operation_type)

def update_sentiment_health(health_score: float, churn_risk_count: int, urgent_count: int):
    """Update sentiment health metrics."""
    monitoring.update_sentiment_health_metrics(health_score, churn_risk_count, urgent_count)

def record_response_suggestion_metrics(
    subscription_tier: str,
    suggestion_type: str,
    status: str,
    duration: float,
    ai_service_status: str = "available",
    quality_score: Optional[float] = None,
    customer_intent: str = "unknown"
):
    """Record response suggestion metrics."""
    monitoring.record_response_suggestion_metrics(
        subscription_tier, suggestion_type, status, duration,
        ai_service_status, quality_score, customer_intent
    )

def update_ai_service_availability(service_name: str, is_available: bool):
    """Update AI service availability status."""
    monitoring.update_ai_service_availability(service_name, is_available)

def update_cache_hit_rate(operation_type: str, hit_rate: float):
    """Update cache hit rate metrics."""
    monitoring.update_cache_hit_rate(operation_type, hit_rate)

# Add-on specific convenience functions
def record_addon_metrics(action: str, user_plan: str, value: int = 1):
    """Record add-on specific metrics."""
    logger.info(f"ADDON_METRIC: {action} | plan: {user_plan} | value: {value}")

def record_marketing_metrics(campaign: str, user_plan: str, conversions: int = 0):
    """Record marketing campaign metrics."""
    logger.info(f"MARKETING_METRIC: {campaign} | plan: {user_plan} | conversions: {conversions}")

def record_access_metrics(feature: str, result: str, user_plan: str):
    """Record feature access metrics."""
    logger.info(f"ACCESS_METRIC: {feature} | result: {result} | plan: {user_plan}")
    monitoring.record_feature_access_attempt(feature, result, user_plan)

def record_addon_purchase_metrics(addon_id: str, variant: str, subscription_tier: str, revenue: float):
    """Record add-on purchase metrics."""
    monitoring.record_addon_purchase(addon_id, variant, subscription_tier, revenue)

def record_addon_usage_metrics(addon_id: str, usage_type: str, subscription_tier: str, amount: int = 1):
    """Record add-on usage metrics."""
    monitoring.record_addon_usage(addon_id, usage_type, subscription_tier, amount)

def record_addon_recommendation_metrics(addon_id: str, context: str, subscription_tier: str):
    """Record add-on recommendation metrics."""
    monitoring.record_addon_recommendation(addon_id, context, subscription_tier)

def record_upsell_metrics(trigger_type: str, addon_id: str, subscription_tier: str):
    """Record upsell notification metrics."""
    monitoring.record_upsell_notification(trigger_type, addon_id, subscription_tier)

# Service-specific loggers
analytics_logger = logging.getLogger("analytics")
billing_logger = logging.getLogger("billing")
messaging_logger = logging.getLogger("messaging")
lemon_squeezy_logger = logging.getLogger("lemon_squeezy")

# Performance monitoring decorator
def monitor_performance(operation_name: str):
    """Decorator to monitor performance of operations."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                logger.debug(f"Operation {operation_name} completed in {duration:.3f}s")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"Operation {operation_name} failed after {duration:.3f}s: {str(e)}")
                raise
        return wrapper
    return decorator

# Audit logging
from enum import Enum

class OperationType(Enum):
    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"
    PAYMENT = "payment"
    FORECAST = "forecast"
    ANALYSIS = "analysis"
    REFUND = "refund"
    GENERATION = "generation"
    LEARNING = "learning"
    REFINEMENT = "refinement"
    UPLOAD = "upload"
    TRAINING = "training"

def log_audit_event(operation_type: OperationType, resource_type: str, user_id: Optional[str] = None,
                   resource_id: Optional[str] = None, details: Optional[dict] = None):
    """Log audit events for security and compliance."""
    audit_data = {
        "operation_type": operation_type.value,
        "resource_type": resource_type,
        "user_id": user_id,
        "resource_id": resource_id,
        "details": details or {},
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    logger.info(f"AUDIT: {audit_data}")

def get_correlation_id() -> str:
    """Get or generate correlation ID for request tracing."""
    import uuid
    return str(uuid.uuid4())
