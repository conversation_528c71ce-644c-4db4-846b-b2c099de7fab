"""
Redis connection service for distributed WebSocket management.
This module provides Redis client initialization and connection pooling
for distributed WebSocket communication across multiple server instances.
"""

import os
import json
import asyncio
from typing import Dict, Any, Optional, List, Set, Callable
import redis.asyncio as redis
from redis.exceptions import RedisError
from datetime import datetime, timezone, timedelta
import time

from app.core.config import settings
from app.services.app_logging import get_logger

# Set up logger
logger = get_logger(__name__)

# Redis client singleton
_redis_client = None

async def get_redis_client() -> Optional[redis.Redis]:
    """
    Get or initialize the Redis client.
    Returns a singleton Redis client instance or None if Redis is not available.
    """
    global _redis_client

    # Return existing client if available
    if _redis_client is not None:
        return _redis_client

    # Return None if Redis is not enabled
    if not settings.REDIS_ENABLED:
        return None

    start_time = time.time()

    try:
        # Initialize Redis client with connection pooling
        _redis_client = redis.Redis.from_url(
            settings.REDIS_URL,
            encoding="utf-8",
            decode_responses=True,
            max_connections=settings.REDIS_MAX_CONNECTIONS,
            socket_timeout=settings.REDIS_SOCKET_TIMEOUT,
            socket_keepalive=True,
            health_check_interval=30
        )

        # Test connection
        await _redis_client.ping()

        duration_ms = int((time.time() - start_time) * 1000)
        logger.info(f"Connected to Redis at {settings.REDIS_URL} in {duration_ms}ms")

        return _redis_client
    except RedisError as e:
        # Log error but don't crash - system can fall back to non-distributed mode
        duration_ms = int((time.time() - start_time) * 1000)
        logger.error(f"Redis connection error: {str(e)} ({duration_ms}ms)")
        logger.warning("Falling back to non-distributed mode")
        _redis_client = None
        return None
    except Exception as e:
        duration_ms = int((time.time() - start_time) * 1000)
        logger.error(f"Unexpected Redis error: {str(e)} ({duration_ms}ms)")
        _redis_client = None
        return None

async def publish_message(channel: str, message: Dict[str, Any]) -> bool:
    """
    Publish a message to a Redis channel.

    Args:
        channel: The Redis channel to publish to
        message: The message to publish (will be JSON serialized)

    Returns:
        bool: True if message was published, False otherwise
    """
    start_time = time.time()

    client = await get_redis_client()
    if not client:
        logger.warning(f"Cannot publish to {channel}: Redis not available")
        return False

    try:
        # Add source identifier and timestamp if not present
        if "_source" not in message:
            message["_source"] = settings.INSTANCE_ID
        if "_timestamp" not in message:
            message["_timestamp"] = datetime.now(timezone.utc).isoformat()

        # Serialize message to JSON
        serialized_message = json.dumps(message)

        # Publish to channel
        receivers = await client.publish(channel, serialized_message)

        duration_ms = int((time.time() - start_time) * 1000)
        logger.debug(f"Published to {channel}: {receivers} receivers ({duration_ms}ms)")

        return True
    except Exception as e:
        duration_ms = int((time.time() - start_time) * 1000)
        logger.error(f"Redis publish error to {channel}: {str(e)} ({duration_ms}ms)")
        return False

async def subscribe_to_channel(channel: str, callback: Callable[[Dict[str, Any]], Any]):
    """
    Subscribe to a Redis channel and process messages.

    Args:
        channel: The Redis channel to subscribe to
        callback: Async function to call with each message
    """
    client = await get_redis_client()
    if not client:
        logger.warning(f"Cannot subscribe to {channel}: Redis not available")
        return

    try:
        # Create pubsub instance
        pubsub = client.pubsub()

        # Subscribe to channel
        await pubsub.subscribe(channel)
        logger.info(f"Subscribed to Redis channel: {channel}")

        # Process messages
        while True:
            try:
                message = await pubsub.get_message(ignore_subscribe_messages=True, timeout=1.0)

                if message and message["type"] == "message":
                    start_time = time.time()

                    try:
                        # Parse message
                        data = json.loads(message["data"])

                        # Skip messages from this instance to prevent loops
                        if data.get("_source") == settings.INSTANCE_ID:
                            continue

                        # Call callback with message data
                        await callback(data)

                        duration_ms = int((time.time() - start_time) * 1000)
                        logger.debug(f"Processed message from {channel} in {duration_ms}ms")
                    except json.JSONDecodeError:
                        logger.error(f"Invalid JSON in Redis message: {message['data']}")
                    except Exception as e:
                        logger.error(f"Error processing Redis message: {str(e)}")

                # Small sleep to prevent CPU spinning
                await asyncio.sleep(0.01)
            except asyncio.CancelledError:
                # Handle cancellation gracefully
                logger.info(f"Redis subscription to {channel} cancelled")
                break
            except Exception as e:
                logger.error(f"Error in Redis subscription loop: {str(e)}")
                # Brief pause before retry
                await asyncio.sleep(1.0)
    except Exception as e:
        logger.error(f"Redis subscription error: {str(e)}")
    finally:
        # Ensure we unsubscribe
        try:
            await pubsub.unsubscribe(channel)
            logger.info(f"Unsubscribed from Redis channel: {channel}")
        except Exception as e:
            logger.error(f"Error unsubscribing from {channel}: {str(e)}")

async def close_redis_connection():
    """
    Close the Redis connection.
    """
    global _redis_client

    if _redis_client is not None:
        try:
            await _redis_client.close()
            logger.info("Redis connection closed")
        except Exception as e:
            logger.error(f"Error closing Redis connection: {str(e)}")
        finally:
            _redis_client = None

# Session management with Redis
class RedisSessionManager:
    """
    Distributed session manager using Redis.
    Manages WebSocket sessions across multiple server instances.
    """

    @staticmethod
    async def add_user_to_session(session_id: str, user_id: str, user_info: Dict[str, Any]) -> bool:
        """
        Add a user to a collaboration session.

        Args:
            session_id: The session identifier
            user_id: The user identifier
            user_info: User information dictionary

        Returns:
            bool: True if successful, False otherwise
        """
        start_time = time.time()

        client = await get_redis_client()
        if not client:
            logger.warning(f"Cannot add user {user_id} to session {session_id}: Redis not available")
            return False

        try:
            # Use pipeline for atomic operations
            pipe = client.pipeline()

            # Store user info in hash
            session_user_key = f"session:{session_id}:users"

            # Clean user_info to ensure it's serializable
            clean_user_info = {}
            for key, value in user_info.items():
                if isinstance(value, (str, int, float, bool)) or value is None:
                    clean_user_info[key] = value

            # Add instance and timestamp info
            clean_user_info["_instance_id"] = settings.INSTANCE_ID
            clean_user_info["_joined_at"] = datetime.now(timezone.utc).isoformat()

            pipe.hset(session_user_key, user_id, json.dumps(clean_user_info))

            # Add to user's sessions set
            user_sessions_key = f"user:{user_id}:sessions"
            pipe.sadd(user_sessions_key, session_id)

            # Update session metadata
            session_meta_key = f"session:{session_id}:meta"
            pipe.hincrby(session_meta_key, "user_count", 1)
            pipe.hset(session_meta_key, "last_activity", datetime.now(timezone.utc).isoformat())
            pipe.hset(session_meta_key, "instance_id", settings.INSTANCE_ID)

            # Set expiration on all keys (24 hours)
            pipe.expire(session_user_key, 86400)
            pipe.expire(user_sessions_key, 86400)
            pipe.expire(session_meta_key, 86400)

            # Execute pipeline
            await pipe.execute()

            # Publish user joined event (don't include in pipeline to ensure atomicity)
            await publish_message(f"session:{session_id}", {
                "type": "user_joined",
                "user_id": user_id,
                "user_info": clean_user_info,
                "timestamp": datetime.now(timezone.utc).isoformat()
            })

            duration_ms = int((time.time() - start_time) * 1000)
            logger.info(f"Added user {user_id} to session {session_id} in {duration_ms}ms")

            return True
        except Exception as e:
            duration_ms = int((time.time() - start_time) * 1000)
            logger.error(f"Redis session error adding user {user_id} to {session_id}: {str(e)} ({duration_ms}ms)")
            return False

    @staticmethod
    async def remove_user_from_session(session_id: str, user_id: str) -> bool:
        """
        Remove a user from a collaboration session.

        Args:
            session_id: The session identifier
            user_id: The user identifier

        Returns:
            bool: True if successful, False otherwise
        """
        start_time = time.time()

        client: Optional[redis.Redis] = await get_redis_client()
        if not client:
            logger.warning(f"Cannot remove user {user_id} from session {session_id}: Redis not available")
            return False

        try:
            # Use pipeline for atomic operations
            pipe = client.pipeline()

            # Remove user from session hash
            session_user_key = f"session:{session_id}:users"
            pipe.hdel(session_user_key, [user_id])

            # Remove session from user's sessions set
            user_sessions_key = f"user:{user_id}:sessions"
            pipe.srem(user_sessions_key, session_id)

            # Update session metadata
            session_meta_key = f"session:{session_id}:meta"
            pipe.hincrby(session_meta_key, "user_count", -1)
            pipe.hset(session_meta_key, "last_activity", datetime.now(timezone.utc).isoformat())
            pipe.hset(session_meta_key, "instance_id", settings.INSTANCE_ID)

            # Execute pipeline
            await pipe.execute()

            # Get remaining user count
            user_count_raw = await client.hget(session_meta_key, "user_count")  # type: ignore
            user_count = int(user_count_raw) if user_count_raw else 0

            # If no users left, clean up session
            if user_count <= 0:
                pipe = client.pipeline()
                pipe.delete(session_user_key)
                pipe.delete(session_meta_key)
                await pipe.execute()
                logger.info(f"Cleaned up empty session {session_id}")

            # Publish user left event
            await publish_message(f"session:{session_id}", {
                "type": "user_left",
                "user_id": user_id,
                "timestamp": datetime.now(timezone.utc).isoformat()
            })

            duration_ms = int((time.time() - start_time) * 1000)
            logger.info(f"Removed user {user_id} from session {session_id} in {duration_ms}ms")

            return True
        except Exception as e:
            duration_ms = int((time.time() - start_time) * 1000)
            logger.error(f"Redis session error removing user {user_id} from {session_id}: {str(e)} ({duration_ms}ms)")
            return False

    @staticmethod
    async def get_session_users(session_id: str) -> List[Dict[str, Any]]:
        """
        Get all users in a session.

        Args:
            session_id: The session identifier

        Returns:
            List of user info dictionaries
        """
        start_time = time.time()

        client = await get_redis_client()
        if not client:
            logger.warning(f"Cannot get users for session {session_id}: Redis not available")
            return []

        try:
            # Get all users from session hash
            session_user_key = f"session:{session_id}:users"
            users_data_raw = await client.hgetall(session_user_key)  # type: ignore
            users_data: Dict[str, str] = users_data_raw if users_data_raw else {}

            # Parse user info
            users = []
            for user_id, user_info_json in users_data.items():
                try:
                    user_info = json.loads(user_info_json)
                    users.append({
                        "user_id": user_id,
                        "user_info": user_info
                    })
                except json.JSONDecodeError:
                    logger.error(f"Invalid JSON in user info for {user_id}: {user_info_json}")

            duration_ms = int((time.time() - start_time) * 1000)
            logger.debug(f"Retrieved {len(users)} users for session {session_id} in {duration_ms}ms")

            return users
        except Exception as e:
            duration_ms = int((time.time() - start_time) * 1000)
            logger.error(f"Redis error getting users for session {session_id}: {str(e)} ({duration_ms}ms)")
            return []

    @staticmethod
    async def broadcast_to_session(session_id: str, message: Dict[str, Any]) -> bool:
        """
        Broadcast a message to all users in a session.

        Args:
            session_id: The session identifier
            message: The message to broadcast

        Returns:
            bool: True if successful, False otherwise
        """
        start_time = time.time()

        # Add metadata if not present
        if "_source" not in message:
            message["_source"] = settings.INSTANCE_ID
        if "_timestamp" not in message:
            message["_timestamp"] = datetime.now(timezone.utc).isoformat()

        # Publish message
        result = await publish_message(f"session:{session_id}", message)

        duration_ms = int((time.time() - start_time) * 1000)
        if result:
            logger.debug(f"Broadcast to session {session_id} in {duration_ms}ms")
        else:
            logger.warning(f"Failed to broadcast to session {session_id} in {duration_ms}ms")

        return result

    @staticmethod
    async def get_active_sessions() -> List[Dict[str, Any]]:
        """
        Get all active sessions.

        Returns:
            List of session info dictionaries
        """
        start_time = time.time()

        client = await get_redis_client()
        if not client:
            logger.warning("Cannot get active sessions: Redis not available")
            return []

        try:
            # Get all session keys
            session_keys = await client.keys("session:*:meta")

            # Get session info
            sessions = []
            for key in session_keys:
                try:
                    # Extract session ID from key
                    session_id = key.split(":")[1]

                    # Get session metadata
                    meta_raw = await client.hgetall(key)  # type: ignore
                    meta: Dict[str, str] = meta_raw if meta_raw else {}

                    # Get user count
                    user_count = int(meta.get("user_count", 0))

                    # Only include active sessions
                    if user_count > 0:
                        # Get session users
                        users = await RedisSessionManager.get_session_users(session_id)

                        sessions.append({
                            "session_id": session_id,
                            "user_count": user_count,
                            "last_activity": meta.get("last_activity"),
                            "instance_id": meta.get("instance_id", "unknown"),
                            "users": users
                        })
                except Exception as e:
                    logger.error(f"Error processing session key {key}: {str(e)}")

            duration_ms = int((time.time() - start_time) * 1000)
            logger.debug(f"Retrieved {len(sessions)} active sessions in {duration_ms}ms")

            return sessions
        except Exception as e:
            duration_ms = int((time.time() - start_time) * 1000)
            logger.error(f"Redis error getting active sessions: {str(e)} ({duration_ms}ms)")
            return []
