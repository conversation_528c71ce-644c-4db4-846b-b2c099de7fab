"""
E-commerce ICP generation service for creating customer personas based on product data.
"""

import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
from bson import ObjectId
from decimal import Decimal

from app.utils.openai_client import generate_content
from app.models.icp import ICP, Demographics, DecisionMaker, PainPoint, Goal, Objection, ContentPreference
from app.models.user import PyObjectId
from app.models.ecommerce import SyncedProduct
from app.services.ecommerce_service import ecommerce_service

logger = logging.getLogger(__name__)


class EcommerceICPGenerator:
    """
    Service for generating ICPs based on e-commerce product data.
    """
    
    async def generate_product_icps(
        self,
        user_id: str,
        store_id: str,
        product_ids: Optional[List[str]] = None,
        count: int = 3
    ) -> List[ICP]:
        """
        Generate ICPs based on product data from an e-commerce store.
        
        Args:
            user_id: User ID
            store_id: Store ID
            product_ids: Specific product IDs to analyze (optional)
            count: Number of ICPs to generate
            
        Returns:
            List of generated ICPs
        """
        try:
            # Get store products
            if product_ids:
                products = await self._get_specific_products(store_id, user_id, product_ids)
            else:
                # Get a sample of products for analysis
                products_data = await ecommerce_service.get_store_products(
                    store_id, user_id, limit=50
                )
                products = products_data.get("products", [])
            
            if not products:
                raise ValueError("No products found for ICP generation")
            
            # Analyze products to extract insights
            product_insights = self._analyze_products(products)
            
            # Generate ICPs using AI
            icps = await self._generate_icps_from_products(
                product_insights, count, user_id, store_id
            )
            
            return icps
            
        except Exception as e:
            logger.error(f"Failed to generate product ICPs: {str(e)}")
            raise
    
    async def generate_category_icps(
        self,
        user_id: str,
        store_id: str,
        category: str,
        count: int = 3
    ) -> List[ICP]:
        """
        Generate ICPs for a specific product category.
        
        Args:
            user_id: User ID
            store_id: Store ID
            category: Product category
            count: Number of ICPs to generate
            
        Returns:
            List of generated ICPs
        """
        try:
            # Get products in the category
            products_data = await ecommerce_service.get_store_products(
                store_id, user_id, limit=100
            )
            products = products_data.get("products", [])
            
            # Filter by category
            category_products = [
                p for p in products 
                if p.category and category.lower() in p.category.lower()
            ]
            
            if not category_products:
                raise ValueError(f"No products found in category: {category}")
            
            # Analyze category-specific products
            product_insights = self._analyze_products(category_products)
            product_insights["category_focus"] = category
            
            # Generate category-specific ICPs
            icps = await self._generate_icps_from_products(
                product_insights, count, user_id, store_id
            )
            
            return icps
            
        except Exception as e:
            logger.error(f"Failed to generate category ICPs: {str(e)}")
            raise
    
    def _analyze_products(self, products: List[SyncedProduct]) -> Dict[str, Any]:
        """
        Analyze products to extract insights for ICP generation.
        
        Args:
            products: List of products to analyze
            
        Returns:
            Product insights dictionary
        """
        insights = {
            "total_products": len(products),
            "price_ranges": {},
            "categories": {},
            "vendors": {},
            "common_tags": {},
            "product_types": {},
            "average_price": 0,
            "price_distribution": {
                "budget": 0,      # < $50
                "mid_range": 0,   # $50-$200
                "premium": 0,     # $200-$500
                "luxury": 0       # > $500
            }
        }
        
        total_price = Decimal("0")
        
        for product in products:
            # Price analysis
            price = product.price
            total_price += price
            
            if price < 50:
                insights["price_distribution"]["budget"] += 1
            elif price < 200:
                insights["price_distribution"]["mid_range"] += 1
            elif price < 500:
                insights["price_distribution"]["premium"] += 1
            else:
                insights["price_distribution"]["luxury"] += 1
            
            # Category analysis
            if product.category:
                category = product.category
                insights["categories"][category] = insights["categories"].get(category, 0) + 1
            
            # Vendor analysis
            if product.vendor:
                vendor = product.vendor
                insights["vendors"][vendor] = insights["vendors"].get(vendor, 0) + 1
            
            # Tag analysis
            for tag in product.tags:
                insights["common_tags"][tag] = insights["common_tags"].get(tag, 0) + 1
            
            # Product type analysis
            if product.product_type:
                ptype = product.product_type
                insights["product_types"][ptype] = insights["product_types"].get(ptype, 0) + 1
        
        # Calculate averages
        if len(products) > 0:
            insights["average_price"] = float(total_price / len(products))
        
        # Get top items
        insights["top_categories"] = sorted(
            insights["categories"].items(), key=lambda x: x[1], reverse=True
        )[:5]
        
        insights["top_vendors"] = sorted(
            insights["vendors"].items(), key=lambda x: x[1], reverse=True
        )[:5]
        
        insights["top_tags"] = sorted(
            insights["common_tags"].items(), key=lambda x: x[1], reverse=True
        )[:10]
        
        return insights
    
    async def _generate_icps_from_products(
        self,
        product_insights: Dict[str, Any],
        count: int,
        user_id: str,
        store_id: str
    ) -> List[ICP]:
        """
        Generate ICPs using AI based on product insights.
        
        Args:
            product_insights: Analyzed product data
            count: Number of ICPs to generate
            user_id: User ID
            store_id: Store ID (used as service_id for compatibility)
            
        Returns:
            List of generated ICPs
        """
        # Create prompt for ICP generation
        prompt = self._create_product_icp_prompt(product_insights, count)
        
        # Generate ICPs using OpenAI
        response = await generate_content(prompt, max_tokens=3000, temperature=0.7)
        
        # Parse the response into ICP objects
        icps = self._parse_product_icp_response(
            response, PyObjectId(store_id), PyObjectId(user_id)
        )
        
        return icps
    
    def _create_product_icp_prompt(self, insights: Dict[str, Any], count: int) -> str:
        """
        Create a prompt for generating ICPs based on product insights.
        
        Args:
            insights: Product analysis insights
            count: Number of ICPs to generate
            
        Returns:
            Formatted prompt string
        """
        # Build product analysis summary
        top_categories = [cat[0] for cat in insights.get("top_categories", [])]
        top_vendors = [vendor[0] for vendor in insights.get("top_vendors", [])]
        top_tags = [tag[0] for tag in insights.get("top_tags", [])]
        
        price_dist = insights.get("price_distribution", {})
        avg_price = insights.get("average_price", 0)
        
        prompt = f"""
Based on the following e-commerce product analysis, generate {count} distinct Ideal Customer Profiles (ICPs) in JSON format.

PRODUCT ANALYSIS:
- Total Products: {insights.get("total_products", 0)}
- Average Price: ${avg_price:.2f}
- Price Distribution:
  * Budget (< $50): {price_dist.get("budget", 0)} products
  * Mid-range ($50-$200): {price_dist.get("mid_range", 0)} products
  * Premium ($200-$500): {price_dist.get("premium", 0)} products
  * Luxury (> $500): {price_dist.get("luxury", 0)} products

- Top Categories: {", ".join(top_categories[:3])}
- Top Brands: {", ".join(top_vendors[:3])}
- Common Features/Tags: {", ".join(top_tags[:5])}

Generate {count} ICPs that would be interested in these products. Each ICP should be returned as a JSON object with the following structure:

{{
  "name": "ICP Name",
  "description": "Brief description of this customer segment",
  "demographics": {{
    "age_range": "25-35",
    "gender": "Mixed",
    "income_range": "$50,000-$75,000",
    "education": "Bachelor's degree",
    "location": "Urban areas",
    "occupation": "Professional"
  }},
  "decision_maker": {{
    "title": "Primary decision maker",
    "department": "Relevant department",
    "influence_level": "High/Medium/Low",
    "decision_criteria": ["criteria1", "criteria2"]
  }},
  "pain_points": [
    {{"description": "Pain point 1", "severity": "High", "frequency": "Daily"}},
    {{"description": "Pain point 2", "severity": "Medium", "frequency": "Weekly"}}
  ],
  "goals": [
    {{"description": "Goal 1", "priority": "High", "timeline": "Short-term"}},
    {{"description": "Goal 2", "priority": "Medium", "timeline": "Long-term"}}
  ],
  "objections": [
    {{"objection": "Price concern", "response": "Value-based response"}},
    {{"objection": "Quality concern", "response": "Quality assurance response"}}
  ],
  "content_preferences": [
    {{"type": "Video", "platform": "YouTube", "engagement_level": "High"}},
    {{"type": "Article", "platform": "Blog", "engagement_level": "Medium"}}
  ],
  "buying_process": "Detailed buying process description"
}}

Focus on creating realistic customer personas that would actually purchase these products. Consider the price points, categories, and features when defining demographics and behaviors.

Return only the JSON array of ICPs, no additional text:
"""
        
        return prompt
    
    def _parse_product_icp_response(
        self, 
        response: str, 
        service_id: PyObjectId, 
        user_id: PyObjectId
    ) -> List[ICP]:
        """
        Parse the AI response into ICP objects.
        
        Args:
            response: AI response containing JSON ICPs
            service_id: Service ID (store ID)
            user_id: User ID
            
        Returns:
            List of ICP objects
        """
        try:
            # Extract JSON from response
            json_start = response.find('[')
            json_end = response.rfind(']') + 1
            
            if json_start == -1 or json_end == 0:
                # Try to find single object
                json_start = response.find('{')
                json_end = response.rfind('}') + 1
                if json_start != -1 and json_end != 0:
                    json_str = response[json_start:json_end]
                    icp_data = [json.loads(json_str)]
                else:
                    raise ValueError("No valid JSON found in response")
            else:
                json_str = response[json_start:json_end]
                icp_data = json.loads(json_str)
            
            icps = []
            for icp_json in icp_data:
                icp = self._create_icp_from_json(icp_json, service_id, user_id)
                icps.append(icp)
            
            return icps
            
        except Exception as e:
            logger.error(f"Failed to parse ICP response: {str(e)}")
            # Return a default ICP if parsing fails
            return [self._create_default_icp(service_id, user_id)]
    
    def _create_icp_from_json(
        self, 
        icp_json: Dict[str, Any], 
        service_id: PyObjectId, 
        user_id: PyObjectId
    ) -> ICP:
        """Create an ICP object from JSON data."""
        # Create demographics
        demo_data = icp_json.get("demographics", {})
        demographics = Demographics(
            industry=demo_data.get("industry", "E-commerce"),
            company_size=demo_data.get("company_size", "Small"),
            annual_revenue=demo_data.get("annual_revenue", "$100K - $1M"),
            employee_count=demo_data.get("employee_count", "1-10"),
            location=demo_data.get("location", ["United States", "Canada"])
        )
        
        # Create decision maker
        dm_data = icp_json.get("decision_maker", {})
        decision_maker = DecisionMaker(
            title=dm_data.get("title", "Primary buyer"),
            department=dm_data.get("department", "Personal"),
            age_range=dm_data.get("age_range", "25-45"),
            years_of_experience=dm_data.get("years_of_experience", "5+"),
            gender=dm_data.get("gender", "Any"),
            education_level=dm_data.get("education_level", "College educated"),
            reporting_to=dm_data.get("reporting_to", "Self")
        )
        
        # Create pain points
        pain_points = []
        for pp_data in icp_json.get("pain_points", []):
            pain_point = PainPoint(
                description=pp_data.get("description", "Finding quality products"),
                severity=pp_data.get("severity", "Medium")
            )
            pain_points.append(pain_point)
        
        # Create goals
        goals = []
        for goal_data in icp_json.get("goals", []):
            goal = Goal(
                description=goal_data.get("description", "Make informed purchasing decisions"),
                priority=goal_data.get("priority", "Medium")
            )
            goals.append(goal)
        
        # Create objections
        objections = []
        for obj_data in icp_json.get("objections", []):
            objection = Objection(
                description=obj_data.get("objection", "Price concerns"),
                counter_point=obj_data.get("response", "Highlight value and quality benefits")
            )
            objections.append(objection)
        
        # Create content preferences
        content_preferences = []
        for cp_data in icp_json.get("content_preferences", []):
            content_pref = ContentPreference(
                content_type=cp_data.get("type", "Social Media"),
                preferred_platforms=cp_data.get("platform", ["Instagram"]) if isinstance(cp_data.get("platform"), list) else [cp_data.get("platform", "Instagram")],
                tone=cp_data.get("tone", "Professional"),
                content_length=cp_data.get("content_length", "Medium")
            )
            content_preferences.append(content_pref)
        
        # Create ICP
        icp = ICP(
            user_id=user_id,
            service_id=service_id,
            name=icp_json.get("name", "E-commerce Customer"),
            description=icp_json.get("description", "Generated from product analysis"),
            demographics=demographics,
            decision_maker=decision_maker,
            pain_points=pain_points,
            goals=goals,
            objections=objections,
            content_preferences=content_preferences,
            buying_process=icp_json.get("buying_process", "Research online, compare options, purchase"),
            success_metrics=["Conversion rate", "Customer satisfaction", "Repeat purchases"],
            is_ai_generated=True
        )
        
        return icp
    
    def _create_default_icp(self, service_id: PyObjectId, user_id: PyObjectId) -> ICP:
        """Create a default ICP when parsing fails."""
        return ICP(
            user_id=user_id,
            service_id=service_id,
            name="E-commerce Customer",
            description="Default customer profile for e-commerce products",
            demographics=Demographics(
                industry="E-commerce",
                company_size="Small",
                annual_revenue="$100K - $1M",
                employee_count="1-10",
                location=["United States", "Canada"]
            ),
            decision_maker=DecisionMaker(
                title="Primary buyer",
                department="Personal",
                age_range="25-45",
                years_of_experience="5+",
                gender="Any",
                education_level="College educated",
                reporting_to="Self"
            ),
            pain_points=[
                PainPoint(
                    description="Finding quality products at reasonable prices",
                    severity="Medium"
                )
            ],
            goals=[
                Goal(
                    description="Make informed purchasing decisions",
                    priority="High"
                )
            ],
            objections=[
                Objection(
                    description="Price concerns",
                    counter_point="Highlight value and quality benefits"
                )
            ],
            content_preferences=[
                ContentPreference(
                    content_type="Social Media",
                    preferred_platforms=["Instagram"],
                    tone="Professional",
                    content_length="Medium"
                )
            ],
            buying_process="Research online, read reviews, compare prices, purchase",
            success_metrics=["Conversion rate", "Customer satisfaction"],
            is_ai_generated=True
        )
    
    async def _get_specific_products(
        self, 
        store_id: str, 
        user_id: str, 
        product_ids: List[str]
    ) -> List[SyncedProduct]:
        """Get specific products by their IDs."""
        # This would need to be implemented in the ecommerce_service
        # For now, get all products and filter
        products_data = await ecommerce_service.get_store_products(
            store_id, user_id, limit=1000
        )
        products = products_data.get("products", [])
        
        # Filter by product IDs
        filtered_products = [
            p for p in products 
            if p.external_product_id in product_ids
        ]
        
        return filtered_products


# Create singleton instance
ecommerce_icp_generator = EcommerceICPGenerator()
