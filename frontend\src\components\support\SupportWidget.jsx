/**
 * Enhanced Support Widget - Enterprise-grade floating support widget
 * Features: Comprehensive support widget system with floating interface and intelligent positioning,
 * detailed widget state management with minimized/expanded states and position persistence, advanced
 * widget features with contextual help suggestions and proactive support triggers, ACE Social's
 * support ecosystem integration with seamless SupportPanel integration and real-time status indicators,
 * widget interaction features including drag-and-drop positioning and keyboard shortcuts, widget
 * customization capabilities with theme options and visibility preferences, real-time widget updates
 * with live support availability and dynamic content loading, and seamless ACE Social platform
 * integration with advanced floating widget orchestration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 */

import {
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Badge,
  Tooltip,
  useTheme,
  alpha,
  Slide,
  Typography,
  Fade,
  Card,
  CardContent,
  Stack,
  Chip,
  Button,
  Menu,
  MenuItem,
  Divider,
  Avatar,
  Snackbar,
  Alert
} from '@mui/material';
import {
  Support as SupportIcon,
  Close as CloseIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon
} from '@mui/icons-material';
import { useNotification } from '../../hooks/useNotification';

// Enhanced support components
import SupportPanel from './SupportPanel';
import useSupportWidget from '../../hooks/useSupportWidget';
import { useAuth } from '../../hooks/useAuth';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Widget states
const WIDGET_STATES = {
  HIDDEN: 'hidden',
  MINIMIZED: 'minimized',
  EXPANDED: 'expanded',
  FULLSCREEN: 'fullscreen',
  DRAGGING: 'dragging'
};

// Widget positions
const WIDGET_POSITIONS = {
  BOTTOM_RIGHT: 'bottom-right',
  BOTTOM_LEFT: 'bottom-left',
  TOP_RIGHT: 'top-right',
  TOP_LEFT: 'top-left'
};

// Enhanced transition component
const EnhancedTransition = forwardRef(function EnhancedTransition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

/**
 * Enhanced Support Widget - Comprehensive floating support widget with advanced features
 * Implements intelligent positioning and enterprise-grade widget management capabilities
 */
const SupportWidget = memo(forwardRef(({
  initialPosition = WIDGET_POSITIONS.BOTTOM_RIGHT,
  enableDragAndDrop = true,
  enableKeyboardShortcuts = true,
  enableProactiveHelp = true,
  enableAnalytics = true,
  autoHideDelay = 0,
  onWidgetStateChange,
  onAnalyticsTrack,
  enableContextualHelp = true,
  maxNotifications = 5
}, ref) => {
  const theme = useTheme();
  const { user } = useAuth();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Core refs for widget management
  const widgetRef = useRef(null);
  const dragRef = useRef({ isDragging: false, startX: 0, startY: 0 });
  const positionRef = useRef(initialPosition);
  const analyticsRef = useRef({
    openCount: 0,
    totalTimeOpen: 0,
    lastOpened: null
  });

  // Enhanced state management
  const [widgetState, setWidgetState] = useState(WIDGET_STATES.MINIMIZED);
  const [position, setPosition] = useState(initialPosition);
  const [hasUnreadUpdates, setHasUnreadUpdates] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const [notifications, setNotifications] = useState([]);
  const [contextualHelp, setContextualHelp] = useState(null);
  const [widgetAnalytics, setWidgetAnalytics] = useState({
    interactions: 0,
    timeSpent: 0,
    lastActivity: new Date().toISOString()
  });
  const [supportStatus] = useState({
    agentsOnline: true,
    queueLength: 0,
    avgResponseTime: '2-4h'
  });

  // Enhanced support widget hook
  const {
    tickets,
    unreadCount,
    loading,
    error,
    connectWebSocket,
    disconnectWebSocket,
    markAsRead,
  } = useSupportWidget();

  // useImperativeHandle for external widget control
  useImperativeHandle(ref, () => ({
    openWidget: () => handleOpenWidget(),
    closeWidget: () => handleCloseWidget(),
    toggleWidget: () => handleToggleWidget(),
    minimizeWidget: () => setWidgetState(WIDGET_STATES.MINIMIZED),
    expandWidget: () => setWidgetState(WIDGET_STATES.EXPANDED),
    setPosition: (newPosition) => handlePositionChange(newPosition),
    getWidgetState: () => widgetState,
    getPosition: () => position,
    getAnalytics: () => widgetAnalytics,
    showNotification: (notification) => handleShowNotification(notification),
    clearNotifications: () => setNotifications([]),
    resetWidget: () => handleResetWidget()
  }), [
    widgetState,
    position,
    widgetAnalytics,
    handleOpenWidget,
    handleCloseWidget,
    handleToggleWidget,
    handlePositionChange,
    handleShowNotification,
    handleResetWidget
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced event handlers
  const handleOpenWidget = useCallback(() => {
    setWidgetState(WIDGET_STATES.EXPANDED);

    // Track analytics
    if (enableAnalytics) {
      setWidgetAnalytics(prev => ({
        ...prev,
        interactions: prev.interactions + 1,
        lastActivity: new Date().toISOString()
      }));
      analyticsRef.current.openCount += 1;
      analyticsRef.current.lastOpened = new Date().toISOString();

      if (onAnalyticsTrack) {
        onAnalyticsTrack({
          action: 'widget_opened',
          timestamp: new Date().toISOString(),
          position,
          unreadCount
        });
      }
    }

    // Mark as read and clear notifications
    if (hasUnreadUpdates) {
      markAsRead();
      setHasUnreadUpdates(false);
    }

    if (onWidgetStateChange) {
      onWidgetStateChange(WIDGET_STATES.EXPANDED);
    }

    announceToScreenReader('Support widget opened');
  }, [
    enableAnalytics,
    onAnalyticsTrack,
    position,
    unreadCount,
    hasUnreadUpdates,
    markAsRead,
    onWidgetStateChange,
    announceToScreenReader
  ]);

  const handleCloseWidget = useCallback(() => {
    setWidgetState(WIDGET_STATES.MINIMIZED);
    setIsFullscreen(false);

    // Track time spent
    if (enableAnalytics && analyticsRef.current.lastOpened) {
      const timeSpent = Date.now() - new Date(analyticsRef.current.lastOpened).getTime();
      analyticsRef.current.totalTimeOpen += timeSpent;

      setWidgetAnalytics(prev => ({
        ...prev,
        timeSpent: prev.timeSpent + timeSpent,
        lastActivity: new Date().toISOString()
      }));

      if (onAnalyticsTrack) {
        onAnalyticsTrack({
          action: 'widget_closed',
          timeSpent,
          timestamp: new Date().toISOString()
        });
      }
    }

    if (onWidgetStateChange) {
      onWidgetStateChange(WIDGET_STATES.MINIMIZED);
    }

    announceToScreenReader('Support widget closed');
  }, [enableAnalytics, onAnalyticsTrack, onWidgetStateChange, announceToScreenReader]);

  const handleToggleWidget = useCallback(() => {
    if (widgetState === WIDGET_STATES.EXPANDED) {
      handleCloseWidget();
    } else {
      handleOpenWidget();
    }
  }, [widgetState, handleCloseWidget, handleOpenWidget]);

  const handlePositionChange = useCallback((newPosition) => {
    setPosition(newPosition);
    positionRef.current = newPosition;

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack({
        action: 'widget_repositioned',
        newPosition,
        timestamp: new Date().toISOString()
      });
    }

    announceToScreenReader(`Widget moved to ${newPosition.replace('-', ' ')}`);
  }, [enableAnalytics, onAnalyticsTrack, announceToScreenReader]);

  const handleShowNotification = useCallback((notification) => {
    setNotifications(prev => {
      const newNotifications = [notification, ...prev].slice(0, maxNotifications);
      return newNotifications;
    });
  }, [maxNotifications]);

  const handleResetWidget = useCallback(() => {
    setWidgetState(WIDGET_STATES.MINIMIZED);
    setPosition(initialPosition);
    setIsFullscreen(false);
    setNotifications([]);
    setContextualHelp(null);
    setWidgetAnalytics({
      interactions: 0,
      timeSpent: 0,
      lastActivity: new Date().toISOString()
    });

    announceToScreenReader('Support widget reset');
  }, [initialPosition, announceToScreenReader]);

  // Enhanced useEffect hooks
  useEffect(() => {
    // Initialize WebSocket connection for real-time updates
    if (user?.id) {
      connectWebSocket();
      return () => disconnectWebSocket();
    }
  }, [user?.id, connectWebSocket, disconnectWebSocket]);

  useEffect(() => {
    // Update unread indicator and contextual help
    setHasUnreadUpdates(unreadCount > 0);

    if (enableContextualHelp && unreadCount > 0) {
      setContextualHelp({
        type: 'unread_updates',
        message: `You have ${unreadCount} unread support updates`,
        action: 'View Updates'
      });
    }
  }, [unreadCount, enableContextualHelp]);

  useEffect(() => {
    // Set up keyboard shortcuts
    if (enableKeyboardShortcuts) {
      const handleKeyDown = (event) => {
        // Ctrl/Cmd + Shift + S to toggle widget
        if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'S') {
          event.preventDefault();
          handleToggleWidget();
        }
        // Escape to close widget
        if (event.key === 'Escape' && widgetState === WIDGET_STATES.EXPANDED) {
          event.preventDefault();
          handleCloseWidget();
        }
      };

      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [enableKeyboardShortcuts, handleToggleWidget, handleCloseWidget, widgetState]);

  useEffect(() => {
    // Auto-hide functionality
    if (autoHideDelay > 0 && widgetState === WIDGET_STATES.EXPANDED) {
      const timer = setTimeout(() => {
        handleCloseWidget();
      }, autoHideDelay);

      return () => clearTimeout(timer);
    }
  }, [autoHideDelay, widgetState, handleCloseWidget]);

  useEffect(() => {
    // Proactive help triggers
    if (enableProactiveHelp) {
      const checkForProactiveHelp = () => {
        // Simulate proactive help logic
        const hasErrors = error !== null;
        const hasLongWaitTime = tickets?.some(t =>
          t.status === 'open' &&
          Date.now() - new Date(t.created_at).getTime() > 24 * 60 * 60 * 1000
        );

        if (hasErrors || hasLongWaitTime) {
          setContextualHelp({
            type: 'proactive_help',
            message: hasErrors ? 'Having trouble? Our support team can help!' : 'Need an update on your ticket?',
            action: 'Get Help'
          });
        }
      };

      const interval = setInterval(checkForProactiveHelp, 60000); // Check every minute
      return () => clearInterval(interval);
    }
  }, [enableProactiveHelp, error, tickets]);

  // Enhanced position calculation
  const getWidgetPosition = useMemo(() => {
    const baseStyles = {
      position: 'fixed',
      zIndex: theme.zIndex.speedDial,
      transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
    };

    switch (position) {
      case WIDGET_POSITIONS.BOTTOM_RIGHT:
        return { ...baseStyles, bottom: 24, right: 24 };
      case WIDGET_POSITIONS.BOTTOM_LEFT:
        return { ...baseStyles, bottom: 24, left: 24 };
      case WIDGET_POSITIONS.TOP_RIGHT:
        return { ...baseStyles, top: 24, right: 24 };
      case WIDGET_POSITIONS.TOP_LEFT:
        return { ...baseStyles, top: 24, left: 24 };
      default:
        return { ...baseStyles, bottom: 24, right: 24 };
    }
  }, [position, theme.zIndex.speedDial]);

  // Don't render if user is not authenticated
  if (!user) {
    return null;
  }

  return (
    <>
      {/* Enhanced Floating Support Widget */}
      <Box
        ref={widgetRef}
        sx={{
          ...getWidgetPosition,
          transform: isDragging ? 'scale(1.05)' : 'scale(1)',
          opacity: widgetState === WIDGET_STATES.HIDDEN ? 0 : 1,
          pointerEvents: widgetState === WIDGET_STATES.HIDDEN ? 'none' : 'auto'
        }}
      >
        {/* Contextual Help Tooltip */}
        {contextualHelp && (
          <Fade in timeout={500}>
            <Card
              sx={{
                position: 'absolute',
                bottom: 80,
                right: 0,
                minWidth: 250,
                maxWidth: 300,
                ...glassMorphismStyles,
                border: `2px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`
              }}
            >
              <CardContent sx={{ p: 2 }}>
                <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
                  <Box>
                    <Typography variant="body2" fontWeight="bold" sx={{ color: ACE_COLORS.DARK }}>
                      {contextualHelp.message}
                    </Typography>
                    <Button
                      size="small"
                      onClick={handleOpenWidget}
                      sx={{
                        mt: 1,
                        color: ACE_COLORS.PURPLE,
                        textTransform: 'none'
                      }}
                    >
                      {contextualHelp.action}
                    </Button>
                  </Box>
                  <IconButton
                    size="small"
                    onClick={() => setContextualHelp(null)}
                    sx={{ color: 'text.secondary' }}
                  >
                    <CloseIcon fontSize="small" />
                  </IconButton>
                </Stack>
              </CardContent>
            </Card>
          </Fade>
        )}

        {/* Enhanced Support Button */}
        <Tooltip
          title={`ACE Social Support • ${supportStatus.avgResponseTime} response • ${supportStatus.agentsOnline ? 'Agents Online' : 'Offline'}`}
          placement="left"
        >
          <Badge
            badgeContent={unreadCount}
            sx={{
              '& .MuiBadge-badge': {
                backgroundColor: ACE_COLORS.YELLOW,
                color: ACE_COLORS.DARK,
                fontWeight: 'bold',
                animation: hasUnreadUpdates ? 'bounce 1s infinite' : 'none',
                '@keyframes bounce': {
                  '0%, 20%, 50%, 80%, 100%': {
                    transform: 'translateY(0)'
                  },
                  '40%': {
                    transform: 'translateY(-3px)'
                  },
                  '60%': {
                    transform: 'translateY(-1px)'
                  }
                }
              }
            }}
            overlap="circular"
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
          >
            <Fab
              aria-label="Open ACE Social Support"
              onClick={handleOpenWidget}
              onMouseDown={enableDragAndDrop ? (e) => {
                setIsDragging(true);
                dragRef.current = {
                  isDragging: true,
                  startX: e.clientX,
                  startY: e.clientY
                };
              } : undefined}
              sx={{
                width: 64,
                height: 64,
                background: `linear-gradient(135deg, ${ACE_COLORS.PURPLE} 0%, ${alpha(ACE_COLORS.PURPLE, 0.8)} 100%)`,
                color: ACE_COLORS.WHITE,
                boxShadow: `0 8px 32px ${alpha(ACE_COLORS.PURPLE, 0.3)}`,
                border: `2px solid ${alpha(ACE_COLORS.WHITE, 0.2)}`,
                '&:hover': {
                  background: `linear-gradient(135deg, ${alpha(ACE_COLORS.PURPLE, 0.9)} 0%, ${alpha(ACE_COLORS.PURPLE, 0.7)} 100%)`,
                  boxShadow: `0 12px 40px ${alpha(ACE_COLORS.PURPLE, 0.4)}`,
                  transform: 'translateY(-2px) scale(1.05)',
                },
                '&:active': {
                  transform: 'translateY(0) scale(0.98)',
                },
                transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',
                animation: hasUnreadUpdates ? 'pulse 2s infinite' : 'none',
                '@keyframes pulse': {
                  '0%': {
                    boxShadow: `0 8px 32px ${alpha(ACE_COLORS.PURPLE, 0.3)}, 0 0 0 0 ${alpha(ACE_COLORS.PURPLE, 0.4)}`
                  },
                  '70%': {
                    boxShadow: `0 8px 32px ${alpha(ACE_COLORS.PURPLE, 0.3)}, 0 0 0 10px ${alpha(ACE_COLORS.PURPLE, 0)}`
                  },
                  '100%': {
                    boxShadow: `0 8px 32px ${alpha(ACE_COLORS.PURPLE, 0.3)}, 0 0 0 0 ${alpha(ACE_COLORS.PURPLE, 0)}`
                  }
                }
              }}
            >
              <SupportIcon sx={{ fontSize: 32 }} />
            </Fab>
          </Badge>
        </Tooltip>

        {/* Widget Settings Menu */}
        {enableDragAndDrop && (
          <IconButton
            size="small"
            onClick={(e) => setMenuAnchorEl(e.currentTarget)}
            sx={{
              position: 'absolute',
              top: -8,
              right: -8,
              backgroundColor: alpha(ACE_COLORS.WHITE, 0.9),
              color: ACE_COLORS.PURPLE,
              width: 24,
              height: 24,
              '&:hover': {
                backgroundColor: ACE_COLORS.WHITE
              }
            }}
          >
            <SettingsIcon fontSize="small" />
          </IconButton>
        )}
      </Box>

      {/* Enhanced Support Dialog/Panel */}
      <Dialog
        open={widgetState === WIDGET_STATES.EXPANDED}
        onClose={handleCloseWidget}
        TransitionComponent={EnhancedTransition}
        maxWidth="lg"
        fullWidth
        fullScreen={isFullscreen}
        PaperProps={{
          sx: {
            height: isFullscreen ? '100vh' : '85vh',
            maxHeight: isFullscreen ? '100vh' : '900px',
            borderRadius: isFullscreen ? 0 : 3,
            overflow: 'hidden',
            ...glassMorphismStyles,
            border: `2px solid ${alpha(ACE_COLORS.PURPLE, 0.3)}`
          },
        }}
        sx={{
          '& .MuiDialog-container': {
            alignItems: isFullscreen ? 'stretch' : 'center',
            justifyContent: isFullscreen ? 'stretch' : 'center',
          },
          '& .MuiBackdrop-root': {
            backgroundColor: alpha(ACE_COLORS.DARK, 0.7),
            backdropFilter: 'blur(8px)'
          }
        }}
      >
        {/* Enhanced Dialog Header */}
        <DialogTitle
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            borderBottom: `2px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
            background: `linear-gradient(135deg, ${ACE_COLORS.PURPLE} 0%, ${alpha(ACE_COLORS.PURPLE, 0.8)} 100%)`,
            color: ACE_COLORS.WHITE,
            py: 2,
            px: 3
          }}
        >
          <Box display="flex" alignItems="center" gap={2}>
            <Avatar
              sx={{
                backgroundColor: alpha(ACE_COLORS.WHITE, 0.2),
                color: ACE_COLORS.WHITE,
                width: 40,
                height: 40
              }}
            >
              <SupportIcon />
            </Avatar>
            <Box>
              <Typography variant="h6" fontWeight="bold" sx={{ color: ACE_COLORS.WHITE }}>
                ACE Social Support Center
              </Typography>
              <Stack direction="row" spacing={1} alignItems="center">
                <Chip
                  size="small"
                  label={supportStatus.agentsOnline ? 'Agents Online' : 'Offline'}
                  sx={{
                    backgroundColor: supportStatus.agentsOnline ? alpha('#4CAF50', 0.2) : alpha('#F44336', 0.2),
                    color: supportStatus.agentsOnline ? '#4CAF50' : '#F44336',
                    border: `1px solid ${supportStatus.agentsOnline ? '#4CAF50' : '#F44336'}`
                  }}
                />
                <Typography variant="caption" sx={{ color: alpha(ACE_COLORS.WHITE, 0.8) }}>
                  {supportStatus.avgResponseTime} avg response
                </Typography>
              </Stack>
            </Box>
          </Box>

          <Stack direction="row" spacing={1}>
            <Tooltip title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}>
              <IconButton
                onClick={() => setIsFullscreen(!isFullscreen)}
                sx={{ color: ACE_COLORS.WHITE }}
              >
                {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
              </IconButton>
            </Tooltip>

            <Tooltip title="Close Support">
              <IconButton
                onClick={handleCloseWidget}
                sx={{
                  color: ACE_COLORS.WHITE,
                  '&:hover': {
                    backgroundColor: alpha(ACE_COLORS.WHITE, 0.1),
                  },
                }}
              >
                <CloseIcon />
              </IconButton>
            </Tooltip>
          </Stack>
        </DialogTitle>

        {/* Enhanced Dialog Content */}
        <DialogContent
          sx={{
            p: 0,
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
            background: `linear-gradient(135deg,
              ${alpha(theme.palette.background.default, 0.95)} 0%,
              ${alpha(ACE_COLORS.PURPLE, 0.02)} 100%)`
          }}
        >
          <SupportPanel
            ref={widgetRef}
            onClose={handleCloseWidget}
            tickets={tickets}
            loading={loading}
            error={error}
            onTicketCreate={() => {
              if (enableAnalytics && onAnalyticsTrack) {
                onAnalyticsTrack({
                  action: 'ticket_created_from_widget',
                  timestamp: new Date().toISOString()
                });
              }
            }}
            onChatStart={() => {
              if (enableAnalytics && onAnalyticsTrack) {
                onAnalyticsTrack({
                  action: 'chat_started_from_widget',
                  timestamp: new Date().toISOString()
                });
              }
            }}
            enableRealTimeUpdates={true}
            enableKeyboardShortcuts={enableKeyboardShortcuts}
            enableAnalytics={enableAnalytics}
            onAnalyticsTrack={onAnalyticsTrack}
          />
        </DialogContent>
      </Dialog>

      {/* Widget Settings Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={() => setMenuAnchorEl(null)}
        PaperProps={{
          sx: {
            ...glassMorphismStyles,
            minWidth: 200
          }
        }}
      >
        <MenuItem onClick={() => handlePositionChange(WIDGET_POSITIONS.BOTTOM_RIGHT)}>
          <Box sx={{ mr: 1 }}>📍</Box>
          Bottom Right
        </MenuItem>
        <MenuItem onClick={() => handlePositionChange(WIDGET_POSITIONS.BOTTOM_LEFT)}>
          <Box sx={{ mr: 1 }}>📍</Box>
          Bottom Left
        </MenuItem>
        <MenuItem onClick={() => handlePositionChange(WIDGET_POSITIONS.TOP_RIGHT)}>
          <Box sx={{ mr: 1 }}>📍</Box>
          Top Right
        </MenuItem>
        <MenuItem onClick={() => handlePositionChange(WIDGET_POSITIONS.TOP_LEFT)}>
          <Box sx={{ mr: 1 }}>📍</Box>
          Top Left
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleResetWidget}>
          <RefreshIcon sx={{ mr: 1 }} />
          Reset Widget
        </MenuItem>
      </Menu>

      {/* Notifications */}
      {notifications.map((notification, index) => (
        <Snackbar
          key={index}
          open={true}
          autoHideDuration={6000}
          onClose={() => setNotifications(prev => prev.filter((_, i) => i !== index))}
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <Alert
            severity={notification.severity || 'info'}
            onClose={() => setNotifications(prev => prev.filter((_, i) => i !== index))}
            sx={{
              ...glassMorphismStyles,
              border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.3)}`
            }}
          >
            {notification.message}
          </Alert>
        </Snackbar>
      ))}
    </>
  );
}));

SupportWidget.displayName = 'SupportWidget';

SupportWidget.propTypes = {
  /** Initial widget position */
  initialPosition: PropTypes.oneOf(Object.values(WIDGET_POSITIONS)),
  /** Enable drag and drop positioning */
  enableDragAndDrop: PropTypes.bool,
  /** Enable keyboard shortcuts */
  enableKeyboardShortcuts: PropTypes.bool,
  /** Enable proactive help suggestions */
  enableProactiveHelp: PropTypes.bool,
  /** Enable analytics tracking */
  enableAnalytics: PropTypes.bool,
  /** Auto-hide delay in milliseconds (0 to disable) */
  autoHideDelay: PropTypes.number,
  /** Custom theme configuration */
  customTheme: PropTypes.object,
  /** Function called when widget state changes */
  onWidgetStateChange: PropTypes.func,
  /** Function called for analytics tracking */
  onAnalyticsTrack: PropTypes.func,
  /** Enable contextual help */
  enableContextualHelp: PropTypes.bool,
  /** Maximum number of notifications to show */
  maxNotifications: PropTypes.number
};

export default SupportWidget;
