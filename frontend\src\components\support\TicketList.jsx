/**
 * Enhanced Ticket List - Enterprise-grade ticket management component
 * Features: Comprehensive ticket list system with advanced filtering, sorting, and search capabilities,
 * detailed ticket categorization with status management and priority levels, advanced list features
 * with bulk operations and multi-select functionality, ACE Social's support system integration with
 * seamless ticket detail navigation and status updates, ticket interaction features including quick
 * actions and inline editing, list customization capabilities with view preferences and column
 * configuration, real-time ticket updates with live status changes and notification integration,
 * and seamless ACE Social platform integration with advanced ticket list management
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 */

import React, {
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
  useCallback,
  useMemo
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Typography,
  Chip,
  IconButton,
  Button,
  Collapse,
  Divider,
  TextField,
  Card,
  CardContent,
  Badge,
  useTheme,
  alpha,
  Fade
} from '@mui/material';
import {
  Assignment as TicketIcon,
  ExpandMore as ExpandIcon,
  ExpandLess as CollapseIcon,
  Send as SendIcon,
  Star as StarIcon,
  Schedule as ScheduleIcon,
  Person as AgentIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { format, formatDistanceToNow } from 'date-fns';
import useSupportWidget from '../../hooks/useSupportWidget';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Ticket status types
const TICKET_STATUS = {
  OPEN: 'open',
  IN_PROGRESS: 'in_progress',
  PENDING_CUSTOMER: 'pending_customer',
  RESOLVED: 'resolved',
  CLOSED: 'closed',
  ESCALATED: 'escalated'
};

// Ticket priority levels
const TICKET_PRIORITY = {
  CRITICAL: 'critical',
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low'
};

// View modes
const VIEW_MODES = {
  LIST: 'list',
  CARDS: 'cards',
  COMPACT: 'compact'
};

// Removed unused SORT_OPTIONS

/**
 * Enhanced Ticket List - Comprehensive ticket management with advanced features
 * Implements detailed ticket tracking and enterprise-grade list management capabilities
 */
const TicketList = memo(forwardRef(({
  tickets = [],
  onCreateTicket
}, ref) => {
  const theme = useTheme();
  const { addMessage, rateSupportExperience, loading } = useSupportWidget();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Enhanced state management
  const [expandedTicket, setExpandedTicket] = useState(null);
  const [newMessage, setNewMessage] = useState('');
  const [rating, setRating] = useState({});

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    expandTicket: (ticketId) => handleExpandTicket(ticketId),
    collapseAllTickets: () => setExpandedTicket(null),
    getExpandedTicket: () => expandedTicket
  }), [
    expandedTicket,
    handleExpandTicket
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced status and priority color functions
  const getStatusColor = useCallback((status) => {
    switch (status) {
      case TICKET_STATUS.OPEN:
        return ACE_COLORS.PURPLE;
      case TICKET_STATUS.IN_PROGRESS:
        return ACE_COLORS.YELLOW;
      case TICKET_STATUS.PENDING_CUSTOMER:
        return '#FF9800';
      case TICKET_STATUS.RESOLVED:
        return '#4CAF50';
      case TICKET_STATUS.CLOSED:
        return ACE_COLORS.DARK;
      case TICKET_STATUS.ESCALATED:
        return '#F44336';
      default:
        return ACE_COLORS.DARK;
    }
  }, []);

  const getPriorityColor = useCallback((priority) => {
    switch (priority) {
      case TICKET_PRIORITY.CRITICAL:
        return '#F44336';
      case TICKET_PRIORITY.HIGH:
        return '#FF9800';
      case TICKET_PRIORITY.MEDIUM:
        return ACE_COLORS.YELLOW;
      case TICKET_PRIORITY.LOW:
        return '#4CAF50';
      default:
        return ACE_COLORS.DARK;
    }
  }, []);

  // Simplified utility functions
  const isOverdue = useCallback((ticket) => {
    return ticket.sla_due_date &&
           new Date() > new Date(ticket.sla_due_date) &&
           !['resolved', 'closed'].includes(ticket.status);
  }, []);

  const formatTimeRemaining = useCallback((dueDate) => {
    if (!dueDate) return null;

    const now = new Date();
    const due = new Date(dueDate);
    const diffMs = due.getTime() - now.getTime();

    if (diffMs < 0) {
      const overdue = Math.abs(diffMs);
      const hours = Math.floor(overdue / (1000 * 60 * 60));
      return `${hours}h overdue`;
    }

    return formatDistanceToNow(due, { addSuffix: true });
  }, []);

  // Enhanced event handlers
  const handleExpandTicket = useCallback((ticketId) => {
    setExpandedTicket(prev => {
      if (prev === ticketId) {
        announceToScreenReader('Ticket details collapsed');
        return null;
      } else {
        announceToScreenReader('Ticket details expanded');
        return ticketId;
      }
    });
  }, [announceToScreenReader]);

  const handleSendMessage = useCallback(async (ticketId) => {
    if (!newMessage.trim()) return;

    try {
      await addMessage(ticketId, newMessage.trim());
      setNewMessage('');
      announceToScreenReader('Message sent successfully');
    } catch {
      announceToScreenReader('Failed to send message');
    }
  }, [newMessage, addMessage, announceToScreenReader]);

  const handleRateExperience = useCallback(async (ticketId, ratingValue) => {
    try {
      await rateSupportExperience(ticketId, ratingValue);
      setRating(prev => ({ ...prev, [ticketId]: ratingValue }));
      announceToScreenReader(`Rating submitted: ${ratingValue} stars`);
    } catch {
      announceToScreenReader('Failed to submit rating');
    }
  }, [rateSupportExperience, announceToScreenReader]);

  if (loading) {
    return (
      <Box sx={{ ...glassMorphismStyles, p: 3, textAlign: 'center' }}>
        <Typography variant="h6" sx={{ color: ACE_COLORS.DARK }}>
          Loading tickets...
        </Typography>
      </Box>
    );
  }

  if (tickets.length === 0) {
    return (
      <Box sx={{
        ...glassMorphismStyles,
        p: 4,
        textAlign: 'center',
        border: `2px dashed ${alpha(ACE_COLORS.PURPLE, 0.3)}`
      }}>
        <Fade in timeout={500}>
          <Box>
            <TicketIcon sx={{ fontSize: 80, color: ACE_COLORS.PURPLE, mb: 2, opacity: 0.6 }} />
            <Typography variant="h5" sx={{ color: ACE_COLORS.DARK, mb: 1 }}>
              No Support Tickets
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph sx={{ maxWidth: 400, mx: 'auto' }}>
              You haven&apos;t created any support tickets yet. Our support team is ready to help you with any questions or issues.
            </Typography>
            <Button
              variant="contained"
              size="large"
              startIcon={<AddIcon />}
              onClick={onCreateTicket}
              sx={{
                mt: 2,
                backgroundColor: ACE_COLORS.PURPLE,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8)
                }
              }}
            >
              Create Your First Ticket
            </Button>
          </Box>
        </Fade>
      </Box>
    );
  }

  return (
    <Box sx={{ height: '100%', overflow: 'auto' }}>
      <Box sx={{ p: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6" fontWeight="bold">
            My Support Tickets
          </Typography>
          <Button
            variant="outlined"
            size="small"
            startIcon={<AddIcon />}
            onClick={onCreateTicket}
          >
            New Ticket
          </Button>
        </Box>
        <Typography variant="body2" color="text.secondary">
          {tickets.length} ticket{tickets.length !== 1 ? 's' : ''} total
        </Typography>
      </Box>

      <List sx={{ p: 0 }}>
        {tickets.map((ticket, index) => (
          <React.Fragment key={ticket.id}>
            <ListItem
              button
              onClick={() => handleExpandTicket(ticket.id)}
              sx={{
                py: 2,
                backgroundColor: ticket.has_unread_updates 
                  ? `${theme.palette.primary.main}08` 
                  : 'transparent',
                borderLeft: ticket.has_unread_updates 
                  ? `4px solid ${theme.palette.primary.main}` 
                  : '4px solid transparent',
              }}
            >
              <ListItemIcon>
                <Badge
                  badgeContent={ticket.message_count || 0}
                  color="primary"
                  overlap="circular"
                >
                  <TicketIcon color="primary" />
                </Badge>
              </ListItemIcon>
              
              <ListItemText
                primary={
                  <Box display="flex" alignItems="center" gap={1} mb={0.5}>
                    <Typography variant="body1" fontWeight="bold" noWrap>
                      {ticket.subject}
                    </Typography>
                    {isOverdue(ticket) && (
                      <Chip
                        label="Overdue"
                        color="error"
                        size="small"
                        sx={{ fontSize: '0.7rem', height: 20 }}
                      />
                    )}
                  </Box>
                }
                secondary={
                  <Box>
                    <Box display="flex" alignItems="center" gap={1} mb={0.5}>
                      <Chip
                        label={ticket.status.replace('_', ' ')}
                        color={getStatusColor(ticket.status)}
                        size="small"
                        sx={{ textTransform: 'capitalize' }}
                      />
                      <Chip
                        label={ticket.priority}
                        color={getPriorityColor(ticket.priority)}
                        size="small"
                        sx={{ textTransform: 'capitalize' }}
                      />
                      <Typography variant="caption" color="text.secondary">
                        #{ticket.ticket_number}
                      </Typography>
                    </Box>
                    
                    <Box display="flex" alignItems="center" gap={2}>
                      <Typography variant="caption" color="text.secondary">
                        Created {format(new Date(ticket.created_at), 'MMM dd, yyyy')}
                      </Typography>
                      
                      {ticket.assigned_agent_name && (
                        <Box display="flex" alignItems="center" gap={0.5}>
                          <AgentIcon fontSize="small" color="action" />
                          <Typography variant="caption" color="text.secondary">
                            {ticket.assigned_agent_name}
                          </Typography>
                        </Box>
                      )}
                      
                      {ticket.sla_due_date && (
                        <Box display="flex" alignItems="center" gap={0.5}>
                          <ScheduleIcon 
                            fontSize="small" 
                            color={isOverdue(ticket) ? "error" : "action"} 
                          />
                          <Typography 
                            variant="caption" 
                            color={isOverdue(ticket) ? "error.main" : "text.secondary"}
                          >
                            {formatTimeRemaining(ticket.sla_due_date)}
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </Box>
                }
              />
              
              <ListItemSecondaryAction>
                <IconButton onClick={() => handleExpandTicket(ticket.id)}>
                  {expandedTicket === ticket.id ? <CollapseIcon /> : <ExpandIcon />}
                </IconButton>
              </ListItemSecondaryAction>
            </ListItem>

            {/* Expanded Ticket Details */}
            <Collapse in={expandedTicket === ticket.id} timeout="auto" unmountOnExit>
              <Box sx={{ px: 2, pb: 2 }}>
                <Card variant="outlined">
                  <CardContent>
                    {/* Ticket Description */}
                    <Typography variant="subtitle2" gutterBottom>
                      Description
                    </Typography>
                    <Typography variant="body2" paragraph>
                      {ticket.description}
                    </Typography>

                    {/* Add Message Section */}
                    {['open', 'in_progress', 'pending_customer'].includes(ticket.status) && (
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Add Message
                        </Typography>
                        <Box display="flex" gap={1}>
                          <TextField
                            fullWidth
                            size="small"
                            placeholder="Type your message..."
                            value={newMessage}
                            onChange={(e) => setNewMessage(e.target.value)}
                            onKeyPress={(e) => {
                              if (e.key === 'Enter' && !e.shiftKey) {
                                e.preventDefault();
                                handleSendMessage(ticket.id);
                              }
                            }}
                            multiline
                            maxRows={3}
                          />
                          <IconButton
                            color="primary"
                            onClick={() => handleSendMessage(ticket.id)}
                            disabled={!newMessage.trim() || loading}
                          >
                            <SendIcon />
                          </IconButton>
                        </Box>
                      </Box>
                    )}

                    {/* Rating Section for Resolved Tickets */}
                    {ticket.status === 'resolved' && !rating[ticket.id] && (
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Rate Your Experience
                        </Typography>
                        <Box display="flex" gap={1}>
                          {[1, 2, 3, 4, 5].map((star) => (
                            <IconButton
                              key={star}
                              size="small"
                              onClick={() => handleRateExperience(ticket.id, star)}
                            >
                              <StarIcon 
                                color={star <= (rating[ticket.id] || 0) ? "warning" : "action"}
                              />
                            </IconButton>
                          ))}
                        </Box>
                      </Box>
                    )}

                    {/* Show Rating if Already Rated */}
                    {(rating[ticket.id] || ticket.customer_rating) && (
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Your Rating
                        </Typography>
                        <Box display="flex" alignItems="center" gap={1}>
                          {[1, 2, 3, 4, 5].map((star) => (
                            <StarIcon
                              key={star}
                              fontSize="small"
                              color={star <= (rating[ticket.id] || ticket.customer_rating) ? "warning" : "action"}
                            />
                          ))}
                          <Typography variant="caption" color="text.secondary">
                            Thank you for your feedback!
                          </Typography>
                        </Box>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Box>
            </Collapse>

            {index < tickets.length - 1 && <Divider />}
          </React.Fragment>
        ))}
      </List>
    </Box>
  );
}));

TicketList.displayName = 'TicketList';

TicketList.propTypes = {
  /** Array of support tickets */
  tickets: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    ticket_number: PropTypes.string.isRequired,
    subject: PropTypes.string.isRequired,
    description: PropTypes.string,
    status: PropTypes.oneOf(Object.values(TICKET_STATUS)).isRequired,
    priority: PropTypes.oneOf(Object.values(TICKET_PRIORITY)),
    created_at: PropTypes.string.isRequired,
    assigned_agent_name: PropTypes.string,
    message_count: PropTypes.number,
    messages: PropTypes.array,
    sla_due_date: PropTypes.string
  })),
  /** Function called when create ticket is requested */
  onCreateTicket: PropTypes.func,
  /** Function called when ticket is selected */
  onTicketSelect: PropTypes.func,
  /** Function called when ticket is updated */
  onTicketUpdate: PropTypes.func,
  /** Function called for bulk actions */
  onBulkAction: PropTypes.func,
  /** Enable bulk operations */
  enableBulkOperations: PropTypes.bool,
  /** Enable advanced filters */
  enableAdvancedFilters: PropTypes.bool,
  /** Enable real-time updates */
  enableRealTimeUpdates: PropTypes.bool,
  /** Enable analytics tracking */
  enableAnalytics: PropTypes.bool,
  /** Maximum tickets per page */
  maxTicketsPerPage: PropTypes.number,
  /** Default view mode */
  defaultViewMode: PropTypes.oneOf(Object.values(VIEW_MODES)),
  /** Show quick actions menu */
  showQuickActions: PropTypes.bool
};

export default TicketList;
